/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.resource.feign;

import com.snszyk.core.launch.constant.AppConstant;
import com.snszyk.core.tool.api.R;
import com.snszyk.resource.entity.Oss;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Oss Feign接口类
 *
 * <AUTHOR>
 */
@FeignClient(
	value = AppConstant.APPLICATION_RESOURCE_NAME,
	fallback = IOssClientFallback.class
)
public interface IOssClient {

	String API_PREFIX = "/client";
	String GET_OSS = API_PREFIX + "/get-oss";

	/**
	 * 获取Oss信息
	 *
	 * @param tenantId 租户id
	 * @param code
	 * @return
	 */
	@GetMapping(GET_OSS)
	R<Oss> getOss(@RequestParam("tenantId") String tenantId, @RequestParam("code") String code);

}
