/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.resource.utils;

import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.resource.feign.IAttachClient;

/**
 * 资源服务工具类
 *
 * <AUTHOR>
 */
public class AttachUtil {

	public static final String PARAM_KEY = "code";

	private static IAttachClient attachClient;

	/**
	 * 获取资源服务构建类
	 *
	 * @return SmsBuilder
	 */
	public static IAttachClient getAttachClient() {
		if (attachClient == null) {
			attachClient = SpringUtil.getBean(IAttachClient.class);
		}
		return attachClient;
	}


}
