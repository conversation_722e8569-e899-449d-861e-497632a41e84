package com.snszyk.resource.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 任务详情dto
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "TaskInfoDto", description = "任务详情dto")
public class TaskInfoDto {

	/**
	 * 是否完成上传（是否已经合并分片）
	 */
	@ApiModelProperty("是否完成上传（是否已经合并分片）")
	private Boolean finished;

	/**
	 * 文件地址
	 */
	@ApiModelProperty("文件地址")
	private String path;

	/**
	 * 上传记录
	 */
	@ApiModelProperty("上传记录")
	private TaskRecordDto taskRecord;
}
