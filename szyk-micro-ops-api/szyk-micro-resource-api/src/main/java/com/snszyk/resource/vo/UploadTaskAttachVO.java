package com.snszyk.resource.vo;

import com.snszyk.resource.entity.UploadTaskAttach;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分片上传附件dto
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UploadTaskAttachVO对象", description = "UploadTaskAttachVO对象")
public class UploadTaskAttachVO extends UploadTaskAttach {

	private static final long serialVersionUID = 1L;

}
