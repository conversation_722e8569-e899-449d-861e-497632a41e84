package com.snszyk.message.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.mp.base.BaseEntity;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 消息推送
 *
 * <AUTHOR> Baomingming
 * @create 2022/8/1
 */
@Data
@Accessors(chain = true)
@TableName("szyk_message_push")
@ApiModel(value = "MessagePush对象", description = "MessagePush对象")
public class MessagePush implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty("主键id")
	@TableId(
		value = "id",
		type = IdType.ASSIGN_ID
	)
	private Long id;
	/**
	 * 消息id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "消息id")
	private Long messageId;
	/**
	 * 收信人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "收信人")
	private Long receiverId;
	/**
	 * 收信人角色id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "收信人角色id")
	private Long roleId;
	/**
	 * 是否已读
	 */
	@ApiModelProperty(value = "是否已读")
	private Integer hasRead;
	/**
	 * 已读时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "已读时间")
	private Date readTime;
	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty("更新时间")
	private Date updateTime;


}
