package com.snszyk.message.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * 消息业务类型枚举类
 *
 * <AUTHOR>
 * @date 2024/07/30 09:56
 **/
public enum MessageBizTypeEnum {

	/**
	 * 资产维保日期超时提醒
	 */
	EXPIRE("维保日期超时", "您的资产【%S】维保日期即将到期（到期时间：%S），请尽快登录电脑端系统进行处理。", 0),


	/**
	 * 盘点管理
	 */
	INVENTORY("盘点管理", "您有新的盘点任务（单号：%S），请及时处理。", 0),

	ARRIVAL("到货单", "您有新的到货单任务（单号：%S），请及时处理。", 0),
	/**
	 * 调拨管理
	 */
	ALLOCATE("调拨管理", "您有新的调拨单任务（单号：%S），请及时处理。", 0),

	/**
	 * 退库管理
	 */
	RETURN("退库管理", "您有新的退库单任务（单号：%S），请及时处理。", 0),

	;

	@Getter
	@Setter
	private String title;

	@Getter
	@Setter
	private String template;

	/**
	 * 消息通知类型[1:流程类型,0:告知型类型(此类型删除此前代办任务时,不删除此类型的数据)]
	 */
	@Getter
	@Setter
	private Integer promptType;

	MessageBizTypeEnum(String title, String content, Integer promptType) {

		this.title = title;
		this.template = content;
		this.promptType = promptType;
	}

	public String content(String... value) {
		return String.format(this.template, (Object[]) value);
	}

}
