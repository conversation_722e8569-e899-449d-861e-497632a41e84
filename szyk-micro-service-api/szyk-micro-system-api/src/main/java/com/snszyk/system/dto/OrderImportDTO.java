package com.snszyk.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 导入订单结果
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class OrderImportDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 导入成功条数
	 */
	@ApiModelProperty("导入成功条数")
	private Integer successCount;

	/**
	 * 导入失败条数
	 */
	@ApiModelProperty("导入失败条数")
	private Integer failCount;

	/**
	 * 下载导入失败数据的key
	 */
	@ApiModelProperty("下载导入失败数据的key")
	private String failReasonDownloadKey;

}
