/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.desk.feign;

import com.snszyk.core.launch.constant.AppConstant;
import com.snszyk.core.mp.support.SzykPage;
import com.snszyk.desk.entity.Notice;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Notice Feign接口类
 *
 * <AUTHOR>
 */
@FeignClient(
	value = AppConstant.APPLICATION_DESK_NAME
)
public interface INoticeClient {

	String API_PREFIX = "/client";
	String TOP = API_PREFIX + "/top";

	/**
	 * 获取notice列表
	 *
	 * @param current
	 * @param size
	 * @return
	 */
	@GetMapping(TOP)
	SzykPage<Notice> top(@RequestParam("current") Integer current, @RequestParam("size") Integer size);

}
