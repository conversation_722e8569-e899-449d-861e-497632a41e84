/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.system.user.vo;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 视图实体类
 *
 * <AUTHOR>
 */
@Data
//@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UserVO对象", description = "UserVO对象")
public class UserVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty("主键id")
	@TableId(
		value = "id",
		type = IdType.ASSIGN_ID
	)
	private Long id;
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty("创建人")
	private Long createUser;
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty("创建部门")
	private Long createDept;
	@DateTimeFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@JsonFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@ApiModelProperty("创建时间")
	private Date createTime;
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty("更新人")
	private Long updateUser;
	@DateTimeFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@JsonFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@ApiModelProperty("更新时间")
	private Date updateTime;
	@ApiModelProperty("业务状态")
	private Integer status;
	@TableLogic
	@ApiModelProperty("是否已删除")
	private Integer isDeleted;

	@ApiModelProperty("租户ID")
	private String tenantId;

	/**
	 * 用户编号
	 */
	private String code;
	/**
	 * 用户平台
	 */
	private Integer userType;
	/**
	 * 账号
	 */
	private String account;
	/**
	 * 昵称
	 */
	private String name;
	/**
	 * 真名
	 */
	private String realName;
	/**
	 * 头像
	 */
	private String avatar;
	/**
	 * 邮箱
	 */
	private String email;
	/**
	 * 手机
	 */
	private String phone;
	/**
	 * 生日
	 */
	private Date birthday;
	/**
	 * 性别
	 */
	private Integer sex;
	/**
	 * 角色id
	 */
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	private String roleId;
	/**
	 * 部门id
	 */
	private String deptId;
	/**
	 * 岗位id
	 */
	private String postId;
	/**
	 * 钉钉用户id
	 */
	private String dingtalkUserId;
	/**
	 * 供应商id
	 */
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@JsonSerialize(using = ToStringSerializer.class)
	private Long supplierId;
	/**
	 * 品牌id
	 */
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@JsonSerialize(using = ToStringSerializer.class)
	private Long brandId;
	/**
	 * 供应商用户过期时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATE)
	private Date expireDate;
	/**
	 * 是否已选择
	 */
	@ApiModelProperty(value = "是否已选择")
	private Integer isSelected;

	/**
	 * 租户名
	 */
	private String tenantName;

	/**
	 * 用户平台名
	 */
	private String userTypeName;

	/**
	 * 角色名
	 */
	private String roleName;

	/**
	 * 部门名
	 */
	private String deptName;

	/**
	 * 岗位名
	 */
	private String postName;

	/**
	 * 性别
	 */
	private String sexName;

	/**
	 * 拓展信息
	 */
	private String userExt;

	/**
	 * 资产供应商
	 */
	private String supplierName;

	/**
	 * 资产品牌
	 */
	private String brandName;

	/**
	 * 角色别名列表
	 */
	private String roleAlias;

	/**
	 * 是否包含子部门人员（0否1是）
	 */
	private Integer isContained;

	private String userIds;

	public UserVO(){
		super();
	}

	public UserVO(Long userId, Long deptId){
		super();
		this.setId(userId);
		this.setDeptId(Func.toStr(deptId));
	}


}
