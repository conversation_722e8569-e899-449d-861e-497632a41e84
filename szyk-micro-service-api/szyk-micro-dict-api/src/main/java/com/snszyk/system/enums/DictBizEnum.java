/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.system.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务字典枚举类
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DictBizEnum {

	/**
	 * 供应商类型
	 */
	SUPPLIER_TYPE("supplier_type"),

	/**
	 * 资产档案字段类型
	 */
	FIELD_TYPE("field_type"),

	/**
	 * 资产档案字段
	 */
	ASSET_FIELD("asset_field"),
	/**
	 * 资产档案字段
	 */
	ASSET_TYPE("asset_type"),
	/**
	 * 资产档案字段
	 */
	ASSET_SOURCE("asset_source"),
	/**
	 * 打印类型
	 */
	DEVICE_TYPE("device_type"),

	/**
	 * 测试
	 */
	TEST("test"),

	;

	final String name;

}
