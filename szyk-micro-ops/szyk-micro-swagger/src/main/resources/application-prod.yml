knife4j:
  cloud:
    routes:
      - name: 授权模块
        uri: 127.0.0.1:91
        location: /szyk-auth/v2/api-docs
      - name: 工作台模块
        uri: 127.0.0.1:91
        location: /szyk-desk/v2/api-docs
      - name: 系统模块
        uri: 127.0.0.1:91
        location: /szyk-system/v2/api-docs
      - name: 用户模块
        uri: 127.0.0.1:91
        location: /szyk-user/v2/api-docs
      - name: 资源模块
        uri: 127.0.0.1:91
        location: /szyk-resource/v2/api-docs
      - name: 消息中心模块
        uri: 127.0.0.1:91
        location: /szyk-message/v2/api-docs
      - name: 资产管理模块
        uri: 127.0.0.1:91
        location: /szyk-asset/v2/api-docs
