#服务器端口
server:
  port: 9111

spring:
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        namespace: szyk_asset
        group: ASSET_GROUP
      config:
        server-addr: 127.0.0.1:8848
        file-extension: yaml
        namespace: szyk_asset
        group: ASSET_GROUP
  # 上传文件大小限制
  servlet:
    multipart:
      max-file-size: 10240MB
      max-request-size: 10240MB
      location: /opt/upload/tmp

#knife4j配置
knife4j:
  #启用
  enable: true
  #基础认证
  basic:
    enable: false
    username: szyk
    password: szyk
  #增强配置
  setting:
    enableSwaggerModels: true
    enableDocumentManage: true
    enableHost: false
    enableHostText: http://localhost
    enableRequestCache: true
    enableFilterMultipartApis: false
    enableFilterMultipartApiMethodType: POST
    language: zh-CN
    enableFooter: false
    enableFooterCustom: true
    footerCustomContent: Copyright © 2021 Szyk All Rights Reserved

#szyk配置
szyk:
  #token配置
  token:
    #是否有状态
    state: false
  #redis序列化方式
  redis:
    serializer-type: protostuff
  #接口配置
  api:
    #报文加密配置
    crypto:
      #启用报文加密配置
      enabled: false
      #使用AesUtil.genAesKey()生成
      aes-key: O2BEeIv399qHQNhD6aGW8R8DEj4bqHXm
      #使用DesUtil.genDesKey()生成
      des-key: jMVCBsFGDQr1USHo
  #jackson配置
  jackson:
    #null自动转空值
    null-to-empty: false
    #大数字自动转字符串
    big-num-to-string: true
    #支持text文本请求,与报文加密同时开启
    support-text-plain: false
  #xss配置
  xss:
    enabled: true
    skip-url:
      - /weixin
      - /notice/submit
  #安全框架配置
  secure:
    #接口放行
    skip-url:
      - /test/**
      - /szyk-swagger/**
      - /szyk-auth/**
    #授权认证配置
    auth:
      - method: ALL
        pattern: /weixin/**
        expression: "hasAuth()"
      - method: POST
        pattern: /dashboard/upload
        expression: "hasTimeAuth(9, 17)"
      - method: POST
        pattern: /dashboard/submit
        expression: "hasAnyRole('administrator', 'admin', 'user')"
    #基础认证配置
    basic:
      - method: ALL
        pattern: /dashboard/info
        username: "szyk"
        password: "szyk"
    #动态签名认证配置
    sign:
      - method: ALL
        pattern: /dashboard/sign
        crypto: "sha1"
    #多终端认证配置
    client:
      - client-id: sword
        path-patterns:
          - /sword/**
      - client-id: saber
        path-patterns:
          - /saber/**
      - client-id: asset
        path-patterns:
          - /prod/**
      - client-id: asset-app
        path-patterns:
          - /prod/**
  #多租户配置
  tenant:
    #多租户增强
    enhance: false
    #多租户授权保护
    license: false
    #动态数据源功能
    dynamic-datasource: false
    #动态数据源全局扫描
    dynamic-global: false
    #多租户字段名
    column: tenant_id
    #排除多租户逻辑
    exclude-tables:
      - szyk_user
  mybatis-plus:
    page-limit: 5000
