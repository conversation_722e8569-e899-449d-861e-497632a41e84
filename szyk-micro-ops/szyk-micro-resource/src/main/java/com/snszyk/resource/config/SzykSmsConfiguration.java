/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.resource.config;

import lombok.AllArgsConstructor;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.sms.props.SmsProperties;
import com.snszyk.resource.builder.sms.SmsBuilder;
import com.snszyk.resource.service.ISmsService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Sms配置类
 *
 * <AUTHOR>
 */
@Configuration
@AllArgsConstructor
public class SzykSmsConfiguration {

	private final SmsProperties smsProperties;

	private final ISmsService smsService;

	private final SzykRedis szykRedis;

	@Bean
	public SmsBuilder smsBuilder() {
		return new SmsBuilder(smsProperties, smsService, szykRedis);
	}

}
