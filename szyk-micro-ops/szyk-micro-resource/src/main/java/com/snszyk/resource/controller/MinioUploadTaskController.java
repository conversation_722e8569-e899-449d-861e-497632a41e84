package com.snszyk.resource.controller;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 上传控制器 - 支持分片上传、断点续传
 * <AUTHOR>
 */
@Slf4j
@Api(value = "文件上传（支持分片上传、断点续传）", tags = "文件上传（支持分片上传、断点续传）")
@RestController
@RequestMapping("/minio/tasks")
public class MinioUploadTaskController {

}
