/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.resource.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.resource.entity.Oss;
import com.snszyk.resource.vo.OssVO;

import java.util.List;

/**
 *  Mapper 接口
 *
 * <AUTHOR>
 */
public interface OssMapper extends BaseMapper<Oss> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param oss
	 * @return
	 */
	List<OssVO> selectOssPage(IPage page, OssVO oss);

}
