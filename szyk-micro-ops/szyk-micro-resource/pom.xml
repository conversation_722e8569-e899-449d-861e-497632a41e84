<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>szyk-micro-ops</artifactId>
        <groupId>com.snszyk</groupId>
        <version>1.3.0.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>szyk-micro-resource</artifactId>
    <name>${project.artifactId}</name>
    <version>${szyk.project.version}</version>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-micro-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-core-boot</artifactId>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-starter-swagger</artifactId>
        </dependency>
        <!--Oss-->
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-starter-oss</artifactId>
        </dependency>
        <!--Sms-->
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-starter-sms</artifactId>
        </dependency>
        <!--MinIO-->
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
        </dependency>
        <!--Alioss-->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>
        <!--AliSms-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
        </dependency>
        <!--腾讯COS-->
        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
        </dependency>
        <!--腾讯SMS-->
        <dependency>
            <groupId>com.github.qcloudsms</groupId>
            <artifactId>qcloudsms</artifactId>
        </dependency>
        <!--QiNiu-->
        <dependency>
            <groupId>com.qiniu</groupId>
            <artifactId>qiniu-java-sdk</artifactId>
        </dependency>
        <!--YunPian-->
        <dependency>
            <groupId>com.yunpian.sdk</groupId>
            <artifactId>yunpian-java-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-micro-resource-api</artifactId>
            <version>${szyk.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-micro-dict-api</artifactId>
            <version>${szyk.project.version}</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.3.10.RELEASE</version>
                <configuration>
                    <mainClass>com.snszyk.resource.ResourceApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
