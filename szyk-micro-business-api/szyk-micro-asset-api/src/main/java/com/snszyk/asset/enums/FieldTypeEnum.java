/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.asset.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 资产档案字段类型枚举类
 *
 * <AUTHOR>
 * @date 2024/07/23 11:35
 **/
@Getter
@AllArgsConstructor
public enum FieldTypeEnum {

	/**
	 * 单选列表
	 */
	SELECT("SELECT", "单选列表"),
	/**
	 * 多选列表
	 */
	MULTISELECT("MULTISELECT", "多选列表"),
	/**
     * 日期
     */
	DATE("DATE", "日期"),
	/**
	 * 日期时间
	 */
	DATETIME("DATETIME", "日期时间"),
	/**
	 * 整数
	 */
	INT("INT", "整数"),
	/**
	 * 小数
	 */
	DECIMAL("DECIMAL", "小数"),
	/**
	 * 单行文本
	 */
	VARCHAR("VARCHAR", "单行文本"),
	/**
	 * 多行文本
	 */
	TEXT("TEXT", "多行文本"),
	/**
	 * 长整数
	 */
	LONG("LONG", "长整数"),

	;

	final String code;
	final String name;

	public static FieldTypeEnum getByCode(String code){
		for (FieldTypeEnum value : FieldTypeEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
