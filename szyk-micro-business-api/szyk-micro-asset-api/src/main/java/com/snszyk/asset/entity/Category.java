/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.asset.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 资产分类表实体类
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@Data
@Accessors(chain = true)
@TableName("asset_category")
@ApiModel(value = "Category对象", description = "资产分类表")
public class Category implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty("主键id")
	@TableId(
		value = "id",
		type = IdType.ASSIGN_ID
	)
	private Long id;
	/**
	 * 父主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("父主键")
	private Long parentId;
	/**
	 * 编码
	 */
	@ApiModelProperty("编码")
	private String code;
	/**
	 * 父编码
	 */
	@ApiModelProperty("父编码")
	private String parentCode;
	/**
	 * 祖级列表
	 */
	@ApiModelProperty(value = "祖级列表")
	private String ancestors;
	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;
	/**
	 * 编码路径
	 */
	@ApiModelProperty(value = "编码路径")
	private String codePath;
	/**
	 * 路径名
	 */
	@ApiModelProperty(value = "路径名")
	private String path;
	/**
	 * 层级
	 */
	@ApiModelProperty(value = "层级")
	private Integer level;
	/**
	 * 年限
	 */
	@ApiModelProperty(value = "年限")
	private String years;
	/**
	 * 打印类型
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "打印类型")
	private Integer deviceType;
	/**
	 * 角色列表
	 */
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@ApiModelProperty(value = "角色列表")
	private String roleId;
	/**
	 * 信息管控系统设备分类编码
	 */
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@ApiModelProperty(value = "信息管控系统设备分类编码")
	private String mcCode;
	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private Integer sort;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 是否已删除
	 */
	@TableLogic
	@ApiModelProperty(value = "是否已删除")
	private Integer isDeleted;

}
