/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.asset.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 财务资产数据表实体类
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@TableName("asset_finance_data")
@ApiModel(value = "FinanceData对象", description = "财务资产数据表")
public class FinanceData extends TenantEntity {
	private static final long serialVersionUID = 1L;

	/**
	 * 导入文件ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "导入文件ID")
	private Long fileId;
	/**
	 * 导入文件名称
	 */
	@ApiModelProperty(value = "导入文件名称")
	private String fileName;
	/**
	 * 处理后的文件ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "处理后的文件ID")
	private Long attachId;


}
