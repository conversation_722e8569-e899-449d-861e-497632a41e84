/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.asset.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 资产呈批件表实体类
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
@Data
@Accessors(chain = true)
@TableName("asset_approval")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Approval对象", description = "资产呈批件表")
public class Approval extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 件号
	 */
	@ApiModelProperty(value = "件号")
	private String no;
	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;
	/**
	 * 发起部门
	 */
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "发起部门")
	private Long sponsorDept;
	/**
	 * 手填发起部门
	 */
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@ApiModelProperty(value = "手填发起部门")
	private String sponsor;
	/**
	 * 承办部门
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "承办部门")
	private Long promoteDept;
	/**
	 * 承办人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "承办人")
	private Long promoteUser;


	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "发起日期")
	private Date promoteDate;
	/**
	 * 审批部门
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "审批部门")
	private Long approveDept;

	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "审批日期")
	private Date approveDate;
	/**
	 * 审批人
	 */
	@ApiModelProperty(value = "审批人")
	private String approveUser;
	/**
	 * 附件
	 */
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@ApiModelProperty(value = "附件")
	private String attachId;


}
