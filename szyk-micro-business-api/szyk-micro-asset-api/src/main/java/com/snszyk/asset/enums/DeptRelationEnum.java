/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.asset.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 出入库单状态枚举类
 *
 * <AUTHOR>
 * @date 2024/11/18 19:35
 **/
@Getter
@AllArgsConstructor
public enum DeptRelationEnum {

	AA("董事会秘书部", "综合办公室（党委办公室、董事会办公室、总经理办公室、国际业务办公室）"),
    AB("办公室", "综合办公室（党委办公室、董事会办公室、总经理办公室、国际业务办公室）"),
	AC("综合服务中心", "直属单位-山东能源集团协同服务公司-综合业务中心"),
	AD("纪委", "纪委（监察专员办公室）"),
	AE("党委组织部", "党委组织部（人力资源部）"),
	AF("人力资源服务中心", "山东能源人力资源公司（人力资源服务中心、人力资源信息共享中心"),
	AG("党委宣传部", "党群工作部（党委宣传部、党委统战部、工会、信访办公室、团委、人民武装部、机关党委"),
	AH("新闻中心", "山东能源传媒公司（新闻中心）"),
	AJ("工会群团工作部", "党群工作部（党委宣传部、党委统战部、工会、信访办公室、团委、人民武装部、机关党委"),
	AK("投资发展部", "战略规划部"),
	AL("资产管理部", "资本运营部"),
	AM("经济运行部", "运营管理部（督察办公室）"),
	AN("财务管理部", "财务管理部"),
	AO("法务中心", "法务合规部"),
	AP("审计中心", "直属单位-山东能源集团协同服务公司-审计业务中心"),
	AQ("安全监察局", "安全监察部（生态环保部）"),
	AR("调度指挥中心", "总调度室"),
	AS("权属企业监事会", "资本运营部"),
	AT("资金管理中心", "山东能源集团财务有限公司（资金管理中心）"),
	AU("高管人员", "高级管理人员"),
	AV("总监总助总师", "总助、副总师、高级技术专家"),
	AW("党委巡查办公室", "党委巡察办公室"),
	AX("督察办公室", "运营管理部（督察办公室）"),
	AY("审计风险部", "审计风险部"),
	AZ("国际业务中心", "直属单位-山东能源集团协同服务公司-综合业务中心"),
	BA("工程监督中心", "工程项目管理部"),
	BB("信息化中心", "数智化部"),
	BC("战略研究院", "战略规划部"),
	BD("冲击地压防治中心", "冲击地压防治中心"),
	BE("财务共享中心", "山东能源财务共享公司（财务共享中心）"),
	BF("技术研究总院", "科技发展公司（技术研究总院）"),
	BG("压煤搬迁办公室", "总调度室"),
	BH("信息中心公共", "山东能源集团"),
	BJ("信息技术公司", "直属单位-山东能源集团协同服务公司-综合业务中心"),
	BK("设备管理中心", "煤炭产业管理部"),
	BL("生产技术中心", "煤炭产业管理部"),
	BM("资本运营部", "资本运营部"),
	BO("化工产业发展中心", "化工产业管理部"),
	BN("集团领导", "领导班子"),

	;

	final String code;
	final String name;

	public static DeptRelationEnum getByCode(String code){
		for (DeptRelationEnum value : DeptRelationEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
