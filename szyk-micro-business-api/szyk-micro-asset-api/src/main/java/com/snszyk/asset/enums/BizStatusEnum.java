/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.asset.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 资产管理业务单据状态枚举类
 *
 * <AUTHOR>
 * @date 2024/07/17 14:13
 **/
@Getter
@AllArgsConstructor
public enum BizStatusEnum {

	/**
	 * 未完成
	 */
	TO_BE_CONTINUED(0, "未完成"),
	/**
	 * 进行中
	 */
	IN_PROGRESS(1, "进行中"),
	/**
	 * 已完成
	 */
	DONE(2, "已完成"),
	/**
     * 已作废
     */
    VOIDED(-1, "已作废"),
	/**
     * 已退回
     */
    RETURNED(-2, "已退回"),
	/**
     * 已提交
     */
    SUBMITTED(10, "已提交"),
	/**
     * 已审核
     */
    AUDITED(20, "已审核"),
	/**
     * 已确认
     */
    CONFIRMED(30, "已确认"),
	/**
     * 已入库
     */
    STORED(40, "已入库"),
	/**
     * 已出库
     */
    OUT_STORED(50, "已出库"),
	/**
     * 已盘点
     */
    CHECKED(60, "已盘点"),
	/**
     * 已调拨
     */
    ALLOTED(70, "已调拨"),
	/**
     * 已报废
     */
    SCRAPED(80, "已报废"),
	/**
	 * 已驳回
	 */
	REJECTED(3, "已驳回"),
	/**
	 * 暂存
	 */
	DRAFT(9, "暂存"),

	;

	final Integer code;
	final String name;

	public static BizStatusEnum getByCode(Integer code){
		for (BizStatusEnum value : BizStatusEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
