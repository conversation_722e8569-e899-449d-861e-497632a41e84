/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.asset.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.asset.entity.InventoryRecord;
import com.snszyk.core.tool.utils.Func;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 资产盘点记录表视图实体类
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "InventoryRecordVO对象", description = "资产盘点记录表")
public class InventoryRecordVO extends InventoryRecord {
	private static final long serialVersionUID = 1L;

	/**
	 * 使用地点id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "使用地点id")
	private Long locationId;

	/**
	 * 使用地点
	 */
	@ApiModelProperty(value = "使用地点")
	private String locationName;

	/**
	 * 盘点状态（0：未盘点，1：已盘点）
	 */
	@ApiModelProperty(value = "盘点状态（0：未盘点，1：已盘点）")
	private Integer inventoryStatus;

	/**
	 * 资产状态
	 */
	@ApiModelProperty(value = "资产状态")
	private Integer assetStatus;

	/**
	 * 使用人
	 */
	@ApiModelProperty(value = "使用人")
	private String userName;

	/**
	 * 使用部门
	 */
	@ApiModelProperty(value = "使用部门")
	private String deptName;

	/**
	 * 盘点人
	 */
	@ApiModelProperty(value = "盘点人")
	private String inventoryUserName;

	/**
	 * 单据状态
	 */
	@ApiModelProperty(value = "状态")
	private String statusName;

	/**
	 * 盘点方式
	 */
	@ApiModelProperty(value = "盘点方式")
	private String typeName;

	/**
	 * 资产名称
	 */
	@ApiModelProperty(value = "资产名称")
	private String assetName;

	/**
	 * 资产规格型号
	 */
	@ApiModelProperty(value = "资产规格型号")
	private String assetModel;

	/**
	 * 资产编号
	 */
	@ApiModelProperty(value = "资产编号")
	private String assetSn;

	/**
	 * 资产分类
	 */
	@ApiModelProperty(value = "资产分类")
	private String assetCategory;
	/**
	 * 资产分类
	 */
	@ApiModelProperty(value = "资产分类")
	private String assetCategoryName;
	/**
	 * 资产品牌
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "资产品牌")
	private Long assetBrand;
	/**
	 * 资产品牌
	 */
	@ApiModelProperty(value = "资产品牌")
	private String assetBrandName;
	/**
	 * 资产类型
	 */
	@ApiModelProperty(value = "资产类型")
	private String assetType;
	/**
	 * 资产类型
	 */
	@ApiModelProperty(value = "资产类型")
	private String assetTypeName;
	/**
	 * 查询关键字
	 */
	@ApiModelProperty(value = "查询关键字")
	private String keywords;
	/**
	 * 租户id
	 */
	@ApiModelProperty(value = "租户id")
	private String tenantId;
	/**
	 * 角色id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "角色id")
	private Long roleId;
	/**
	 * 资产编号列表
	 */
	@ApiModelProperty(value = "资产编号列表")
	private String assetCodes;

	@ApiModelProperty(value = "资产分类列表")
	private List<String> categoryCodeList;

	public InventoryRecordVO(){
		super();
	}

	public InventoryRecordVO(Long inventoryId){
		super();
		this.setInventoryId(inventoryId);
	}

	public InventoryRecordVO(Long inventoryId, Long deptId, Long locationId, Integer status){
		super();
		this.setInventoryId(inventoryId);
		if(Func.isNotEmpty(deptId)){
			this.setDeptId(deptId);
		}
		if(Func.isNotEmpty(locationId)){
			this.setLocationId(locationId);
		}
		this.setStatus(status);
	}

}
