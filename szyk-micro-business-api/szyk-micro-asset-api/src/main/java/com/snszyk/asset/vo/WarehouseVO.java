/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.asset.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.asset.entity.Warehouse;
import com.snszyk.common.utils.BizCodeUtil;
import com.snszyk.core.tool.utils.Func;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

/**
 * 出|入|退库单	视图实体类
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
@Data
@Slf4j
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "WarehouseVO对象", description = "出|入|退库单	")
public class WarehouseVO extends Warehouse {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "开始时间")
	private String startTime;
	@ApiModelProperty(value = "结束时间")
	private String endTime;
	@JsonSerialize(using = ToStringSerializer.class)
	private Long roleId;
	@JsonSerialize(using = ToStringSerializer.class)
	private Long inId;
	private String supplierName;
	private String useDeptName;
	private String operateUserName;
	private String operateDeptName;
	private String receiveUserName;
	private String locationName;
	private String createUserName;
	private String updateUserName;
	private String statusName;


//	public void setOnLibrary(Integer onlibrary){
//		this.onlibrary = onlibrary;
//	}
	public static Warehouse generateAuditEntity(Long id,boolean flag){
		Warehouse w = new Warehouse();
		w.setId(id);
		if(flag){
			w.setStatus(2);
		}else{
			w.setStatus(0);
		}
		return w;

	}
	public Warehouse generateAuditEntity(boolean flag){
		if(flag){
			setStatus(2);
		}else{
			setStatus(0);
		}
		return this;

	}

	public Warehouse generateEntity() {
		if(getId() != null){
			log.info("进行修改时退出");
			return this;
		}
		if(Func.isNotEmpty(getNo())){
			return this;
		}
//		String type = "Z";
//		if(getAssetType() == 2){
//			type = "H";
//		}
		switch (getWarehouseType()){
			case 1:
				this.setNo(BizCodeUtil.generate("RKD"));
				setStatus(2);
				break;
			case 2:
				this.setNo(BizCodeUtil.generate("CKD"));
				setStatus(2);
				break;
			case 3:
				this.setNo(BizCodeUtil.generate("TKD"));
				break;
		}
		return this;
	}

	public WarehouseVO configConsumOut() {
		setAssetType(2);
		setWarehouseType(2);
		return this;
	}
	public WarehouseVO configConsumIn() {
		// 耗材默认呈批件入库
		setInType(1);
		setAssetType(2);
		setWarehouseType(1);
		getDetails().forEach(d -> {
			d.setCreateUser(getOperateUser());
			d.setCreateTime(getOperateDate());
		});
		return this;
	}
	public WarehouseVO configAssetOut() {
		setAssetType(1);
		setWarehouseType(2);
		return this;
	}

	/**
	 * 配置入库信息
	 */
	public WarehouseVO configAssetIn() {
		setAssetType(1);
		setWarehouseType(1);
		if(getDetails() != null){
			setSumNum(getDetails().size());
		}
		return this;
	}

	/**
	 * 配置退单信息
	 * @return
	 */
	public WarehouseVO configAssetBack() {
		setAssetType(1);
		setWarehouseType(3);
		return this;
	}


}
