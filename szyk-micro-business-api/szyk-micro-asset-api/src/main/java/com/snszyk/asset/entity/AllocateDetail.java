package com.snszyk.asset.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 调拨信息详情
 */
@Data
@TableName("asset_allocate_detail")
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "调拨详情信息", description = "调拨详情信息")
public class AllocateDetail implements Serializable {

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("主键id")
	@TableId(
		value = "id",
		type = IdType.ASSIGN_ID
	)
	private Long id;
	/**
	 * 调拨单id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long allocateId;
	/**
	 * 资产id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long assetId;
	/**
	 * 原使用部门id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("原使用部门id")
	private Long originalUseDept;
	/**
	 * 原使用人id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("原使用人id")
	private Long originalUseUser;
	/**
	 * 排序
	 */
	@ApiModelProperty("排序")
	private Integer sort;
	/**
	 * 是否删除
	 */
	@ApiModelProperty("是否已删除")
	private Integer isDeleted;


}
