package com.snszyk.asset.dto;

import com.snszyk.asset.entity.Asset;
import com.snszyk.asset.entity.Warehouse;
import com.snszyk.asset.entity.WarehouseDetail;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.user.entity.User;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AssetFlowDTO implements Serializable {
	@ApiModelProperty(value = "资产编码")
	private String assetCode;

	@ApiModelProperty(value = "资产名称")
	private String assetName;

	@ApiModelProperty(value = "业务ID")
	public Long businessId;
	@ApiModelProperty(value = "业务编号")
	public String businessNo;

	@ApiModelProperty(value = "业务名称")
	public String businessName;


	@ApiModelProperty(value = "创建时间")
	public Date date;

	@ApiModelProperty(value = "创建人")
	public String userName;


	@ApiModelProperty(value = "创建部门")
	public String deptName;
	private Integer status;


	/**
	 * 调拨信息
	 * @param allocateDTO 调拨信息
	 * @param dto 资产信息
	 * @return
	 */
	public AssetFlowDTO generate(AllocateDTO allocateDTO, AssetDTO dto){
		this.assetCode = dto.getCode();
		this.assetName  = dto.getName();
		this.businessId = allocateDTO.getId();
		this.businessNo = allocateDTO.getNo();
		this.businessName = "调拨";
		this.date = allocateDTO.getCreateTime();
		this.userName = allocateDTO.getReceiveUserName();
		this.deptName = dto.getUseDeptName();
		this.status = dto.getStatus();
		return this;
	}
	/**
	 * 资产入库信息
	 * @return
	 */
	public AssetFlowDTO generate(WarehouseDTO dto, WarehouseDetail detail){
		this.assetCode = detail.getCode();
		this.assetName  = detail.getName();
		this.businessId = dto.getId();
		this.businessNo = dto.getNo();
		this.businessName = Warehouse.WAREHOUSE_TYPE[dto.getWarehouseType()];
		this.date = dto.getOperateDate();
		this.userName = dto.getOperateUserName();
		this.deptName = dto.getOperateDeptName();
		this.status = dto.getStatus();
		return this;
	}

	public AssetFlowDTO generate(AssetWarehouseDTO dto,AssetDTO assetDTO){
		this.assetCode = assetDTO.getCode();
		this.assetName  = assetDTO.getName();
		this.businessId = assetDTO.getWarehouseId();
		this.businessNo = dto.getNo();
		this.businessName = Warehouse.WAREHOUSE_TYPE[1];
		this.date = dto.getOperateDate();
		this.userName = dto.getOperateUserName();
		this.deptName = dto.getOperateDeptName();
		return this;
	}

	/**
	 *
	 * @param assetDTO 资产信息
	 * @param warehouseDTOS 出入退库信息
	 * @param allocateDTOS 调拨信息
	 * @return
	 */
	public static List<AssetFlowDTO> generate(AssetDTO assetDTO,List<WarehouseDTO> warehouseDTOS,List<AllocateDTO>  allocateDTOS){
		List<AssetFlowDTO> list = new ArrayList<>();
		//if(assetDTO != null && assetDTO.getIn() != null){
		//	list.add(new AssetFlowDTO().generate(assetDTO.getIn(),assetDTO));
		//}

		//遍历入库出库信息
		warehouseDTOS.forEach(warehouse -> {
			//遍历资产信息
			warehouse.getDetails().forEach(detail -> {
				//创建返回信息
				AssetFlowDTO dto = new AssetFlowDTO();
				list.add(dto.generate(warehouse, detail));
			});
		});
		//处理调拨信息
		allocateDTOS.forEach(allocate -> {
			AssetFlowDTO dto = new AssetFlowDTO();
			list.add(dto.generate(allocate, assetDTO));
		});

		list.sort((o1, o2) -> o1.getDate().compareTo(o2.getDate()));
		return list;
	}




}
