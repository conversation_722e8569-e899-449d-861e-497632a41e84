/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.asset.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.asset.entity.Allocate;
import com.snszyk.common.utils.BizCodeUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 资产到货单表视图实体类
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AllocateVO", description = "调拨信息表")
public class AllocateVO extends Allocate {
	private static final long serialVersionUID = 1L;

	/**
	 * 查询条件-开始时间
	 */
	@ApiModelProperty(value = "查询条件-开始时间")
	private String startTime;

	/**
	 * 查询条件-开始时间
	 */
	@ApiModelProperty(value = "查询条件-开始时间")
	private String endTime;

	/**
	 * 角色id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "角色id")
	private Long roleId;

	/**
	 * 接收部门
	 */
	@ApiModelProperty(value = "接收部门")
	private String receiveDeptName;

	/**
	 * 调拨部门
	 */
	@ApiModelProperty(value = "调拨部门")
	private String allocateDeptName;
	/**
	 * 接收人
	 */
	@ApiModelProperty(value = "接收人")
	private String receiveUserName;

	/**
	 * 审批人
	 */
	@ApiModelProperty(value = "审批人")
	private String auditUserName;

	/**
	 * 存放地点
	 */
	@ApiModelProperty(value = "存放地点")
	private String locationName;

	/**
	 * 存放地点路径
	 */
	@ApiModelProperty(value = "存放地点路径")
	private String locationPath;

	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private String statusName;

	/**
	 * 是否综服中心管理员接收
	 */
	@ApiModelProperty(value = "是否综服中心管理员接收")
	private Boolean adminAccept;


	public Allocate generateEntity(){
		if(this.getId() == null){
			this.setNo(BizCodeUtil.generate("DBD"));
		}
		return this;
	}



}
