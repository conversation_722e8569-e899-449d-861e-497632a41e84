/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.asset.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.snszyk.asset.entity.Category;
import com.snszyk.core.tool.node.INode;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * 资产分类表视图实体类
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CategoryVO对象", description = "资产分类表")
public class CategoryVO extends Category implements INode<CategoryVO> {

	private static final long serialVersionUID = 1L;

	/**
	 * 子孙节点
	 */
	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	private List<CategoryVO> children;

	/**
	 * 是否有子孙节点
	 */
	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	private Boolean hasChildren;

	@Override
	public List<CategoryVO> getChildren() {
		if (this.children == null) {
			this.children = new ArrayList<>();
		}
		return this.children;
	}

	/**
	 * 上级分类
	 */
	private String parentName;

	/**
	 * 查询关键字
	 */
	private String keywords;

	/**
	 * 查询来源
	 */
	private String source;

	/**
	 * 打印类型
	 */
	private String deviceTypeName;

	/**
	 * 角色id
	 */
	private Long selectRoleId;

	/**
	 * 是否选择
	 */
	private Integer isSelected;

	/**
	 * 设备分类名称
	 */
	private String mcName;


}
