/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.asset.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 调拨业务单据状态枚举类
 *
 * <AUTHOR>
 * @date 2024/09/25 09:35
 **/
@Getter
@AllArgsConstructor
public enum AllocateStatusEnum {

	/**
	 * 已撤回
	 */
	CANCELED(0, "已撤回"),
	/**
	 * 待接收
	 */
	WAIT_RECEIVE(1, "待接收"),
	/**
     * 已完成
     */
    DONE(2, "已完成"),
	/**
	 * 待确认
	 */
	WAIT_CONFIRM(3, "待确认"),
	/**
	 * 暂存
	 */
	DRAFT(9, "暂存"),
	;

	final Integer code;
	final String name;

	public static AllocateStatusEnum getByCode(Integer code){
		for (AllocateStatusEnum value : AllocateStatusEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
