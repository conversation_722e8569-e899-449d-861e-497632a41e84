/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.asset.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 供应商表实体类
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Data
@TableName("asset_supplier")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Supplier对象", description = "供应商表")
public class Supplier extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 编号
	 */
	@ApiModelProperty(value = "编号")
	private String no;
	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;
	/**
	 * 类型
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "类型（字典：supplier_type）")
	private Integer type;
	/**
	 * 开户银行
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "开户银行id")
	private Long bankId;
	/**
	 * 开户银行
	 */
	@ApiModelProperty(value = "开户银行名称")
	private String bankName;
	/**
	 * 银行账号
	 */
	@ApiModelProperty(value = "银行账号")
	private String bankAccount;
	/**
	 * 联行号
	 */
	@ApiModelProperty(value = "联行号")
	private String interbankNumber;
	/**
	 * 纳税人识别码
	 */
	@ApiModelProperty(value = "纳税人识别码")
	private String tin;
	/**
	 * 营业执照信息
	 */
	@ApiModelProperty(value = "营业执照信息")
	private String businessLicense;
	/**
	 * 注册信息
	 */
	@ApiModelProperty(value = "注册信息")
	private String registerInfo;
	/**
	 * 建档日期
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "建档日期")
	private Date archiveDate;
	/**
	 * 地址
	 */
	@ApiModelProperty(value = "地址")
	private String address;
	/**
	 * 联系人
	 */
	@ApiModelProperty(value = "联系人")
	private String contact;
	/**
	 * 联系方式
	 */
	@ApiModelProperty(value = "联系方式")
	private String tel;
	/**
	 * 是否导入
	 */
	@ApiModelProperty(value = "是否导入")
	private Integer isImported;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 附件
	 */
	@ApiModelProperty(value = "附件")
	private String attachId;

}
