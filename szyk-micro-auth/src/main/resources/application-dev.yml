#服务器端口
server:
  port: 9110

#数据源配置
spring:
  datasource:
    url: ${szyk.datasource.dev.url}
    username: ${szyk.datasource.dev.username}
    password: ${szyk.datasource.dev.password}

#第三方登陆
social:
  enabled: true
  domain: http://127.0.0.1:1888

sso:
  # 客户端id
  clientId: e50eb3080c14d442dcb9
  # 客户端密钥
  clientSecret: e228f3deec741d4a63fa60b01543152f4958
  # 获取授权码接口
  authorizeRequestUrl: https://iamtest.ykjt.cc/esc-sso/oauth2.0/authorize
  # 回调地址
  redirectUrl: http://10.0.21.16:91/#/sso/callback
  # 获取统一认证token接口
  tokenRequestUrl: https://iamtest.ykjt.cc/esc-sso/oauth2.0/accessToken
  # 获取统一认证用户信息
  userInfoRequestUrl: https://iamtest.ykjt.cc/esc-sso/oauth2.0/profile
  # 以下配置暂未使用，可用于账号反向开通同步
  appId: 2099509632927879468
  apiKey: 56c4ef2c71694206
  apiSecret: 33613fd52b95417c82523f4960db0f19

