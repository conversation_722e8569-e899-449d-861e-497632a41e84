/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.auth.granter;

import com.snszyk.auth.properties.SsoProperties;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.social.props.SocialProperties;
import com.snszyk.system.feign.ISysClient;
import com.snszyk.system.user.feign.IUserClient;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerEndpointsConfigurer;
import org.springframework.security.oauth2.provider.CompositeTokenGranter;
import org.springframework.security.oauth2.provider.TokenGranter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 自定义拓展TokenGranter
 *
 * <AUTHOR>
 */
public class SzykTokenGranter {

	/**
	 * 自定义tokenGranter
	 */
	public static TokenGranter getTokenGranter(final AuthenticationManager authenticationManager, final AuthorizationServerEndpointsConfigurer endpoints, SzykRedis szykRedis, IUserClient userClient, SocialProperties socialProperties, SsoProperties ssoProperties, ISysClient sysClient) {
		// 默认tokenGranter集合
		List<TokenGranter> granters = new ArrayList<>(Collections.singletonList(endpoints.getTokenGranter()));
		// 增加验证码模式
		granters.add(new CaptchaTokenGranter(authenticationManager, endpoints.getTokenServices(), endpoints.getClientDetailsService(), endpoints.getOAuth2RequestFactory(), szykRedis));
		// 增加第三方登陆模式
		granters.add(new SocialTokenGranter(endpoints.getTokenServices(), endpoints.getClientDetailsService(), endpoints.getOAuth2RequestFactory(), userClient, socialProperties));
		// 增加第三方登陆模式
		granters.add(new PhoneTokenGranter(endpoints.getTokenServices(), endpoints.getClientDetailsService(), endpoints.getOAuth2RequestFactory(), userClient, szykRedis));
		// 增加sso登录模式
		granters.add(new SsoTokenGranter(endpoints.getTokenServices(), endpoints.getClientDetailsService(), endpoints.getOAuth2RequestFactory(), userClient, ssoProperties, sysClient));
		// 组合tokenGranter集合
		return new CompositeTokenGranter(granters);
	}

}
