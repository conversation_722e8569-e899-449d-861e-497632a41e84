package com.snszyk.auth.granter;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiUserGetuserinfoRequest;
import com.dingtalk.api.response.OapiUserGetuserinfoResponse;
import com.snszyk.auth.constant.AuthConstant;
import com.snszyk.auth.service.SzykUserDetails;
import com.snszyk.auth.utils.AccessTokenUtil;
import com.snszyk.auth.utils.TokenUtil;
import com.snszyk.common.cache.CacheNames;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.support.Kv;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.WebUtil;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Role;
import com.snszyk.system.user.entity.User;
import com.snszyk.system.user.entity.UserInfo;
import com.snszyk.system.user.feign.IUserClient;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.oauth2.common.exceptions.InvalidGrantException;
import org.springframework.security.oauth2.common.exceptions.UserDeniedAuthorizationException;
import org.springframework.security.oauth2.provider.*;
import org.springframework.security.oauth2.provider.token.AbstractTokenGranter;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;

import javax.servlet.http.HttpServletRequest;
import java.time.Duration;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 第三方登录认证类
 *
 * <AUTHOR>
 */
@Slf4j
public class PhoneTokenGranter extends AbstractTokenGranter {
	private static final String GRANT_TYPE = "phone";
	private static final Integer AUTH_SUCCESS_CODE = 2000;

	private final IUserClient userClient;
	private SzykRedis szykRedis;

	protected PhoneTokenGranter(AuthorizationServerTokenServices tokenServices, ClientDetailsService clientDetailsService, OAuth2RequestFactory requestFactory, IUserClient userClient, SzykRedis szykRedis) {
		super(tokenServices, clientDetailsService, requestFactory, GRANT_TYPE);
		this.userClient = userClient;
		this.szykRedis = szykRedis;
	}

	@Override
	protected OAuth2Authentication getOAuth2Authentication(ClientDetails client, TokenRequest tokenRequest) {
		// 请求头租户信息
		HttpServletRequest request = WebUtil.getRequest();
		// 钉钉登录
		String dingTalkAuthCode = request.getHeader("auth-code");
		log.info("当前钉钉授权码====================：{}", dingTalkAuthCode);
		String roleId = request.getHeader("role-Id");
		log.info("当前用户角色ID====================：{}", roleId);
		if (dingTalkAuthCode == null) {
			throw new UserDeniedAuthorizationException(TokenUtil.AUTH_CODE_NOT_CORRECT);
		}
		String accessToken;
		String dingTalkUserId = null;
		if(Func.isEmpty(roleId)){
			try {
				accessToken = AccessTokenUtil.getToken();
				log.info("请求钉钉API获取DingTalk—AccessToken：=================={}", accessToken);
			} catch (Exception e) {
				e.printStackTrace();
				return null;
			}
			// 2.获取用户信息
			DingTalkClient dingTalkClient = new DefaultDingTalkClient(AuthConstant.URL_GET_USER_INFO);
			OapiUserGetuserinfoRequest userinfoRequest = new OapiUserGetuserinfoRequest();
			userinfoRequest.setCode(dingTalkAuthCode);
			userinfoRequest.setHttpMethod("GET");
			OapiUserGetuserinfoResponse response;
			try {
				response = dingTalkClient.execute(userinfoRequest, accessToken);
				// 3.查询得到当前用户的userId
				// 获得到userId之后应用应该处理应用自身的登录会话管理（session）,避免后续的业务交互（前端到应用服务端）每次都要重新获取用户身份，提升用户体验
				dingTalkUserId = response.getUserid();
				if(dingTalkUserId != null){
					log.info("请求钉钉API获取DingTalk—UserId：=================={}", dingTalkUserId);
					String mobile = AccessTokenUtil.getUserMobile(dingTalkUserId, accessToken);
					// 根据手机号查询系统用户信息
					R<List<User>> userListResult = userClient.userInfoByPhones(Func.toStrList(mobile));
					if(userListResult.isSuccess() && Func.isNotEmpty(userListResult.getData())){
						User userEntity = userListResult.getData().get(0);
						// 用户绑定钉钉userid
						userClient.updateDingTalkUserId(userEntity.getId(), dingTalkUserId);
					}
					// dingtalk_user_id缓存
					szykRedis.setEx(CacheNames.USER_DINGTALK_KEY + dingTalkAuthCode,
						dingTalkUserId, Duration.ofMinutes(5));
				}
			} catch (ApiException e) {
				log.error("请求钉钉API获取DingTalk—UserId错误！", e.getErrMsg());
				e.printStackTrace();
			}
		}
		if(dingTalkUserId == null){
			// 从缓存中取dingtalk_user_id
			dingTalkUserId = szykRedis.get(CacheNames.USER_DINGTALK_KEY + dingTalkAuthCode);
			log.info("从缓存中取出的用户DingTalk—UserId：=================={}", dingTalkUserId);
			szykRedis.del(CacheNames.USER_DINGTALK_KEY + dingTalkAuthCode);
		}
		R<UserInfo> result = userClient.userInfoByDingTalk(dingTalkUserId);
		Map<String, String> parameters = new LinkedHashMap<>(tokenRequest.getRequestParameters());
		// 开放平台来源
		//String phone = parameters.get("phone");
		// 远程调用，获取认证信息
		//R<UserInfo> result = userClient.userInfoByPhone(tenantId, phone);
		SzykUserDetails szykUserDetails;
		if (result.isSuccess()) {
			User user = result.getData().getUser();
			if(Func.isNotEmpty(roleId)){
				Role role = SysCache.getRole(Func.toLong(roleId));
				user.setRoleId(roleId);
				result.getData().setUser(user);
				result.getData().setRoles(Func.toStrList(role.getRoleAlias()));
			}
			Kv detail = result.getData().getDetail();
			if (user == null || user.getId() == null) {
				throw new InvalidGrantException("social grant failure, user is null");
			}
			szykUserDetails = new SzykUserDetails(user.getId(),
				user.getTenantId(), result.getData().getOauthId(), user.getName(), user.getRealName(), user.getDeptId(), user.getPostId(), user.getRoleId(), Func.join(result.getData().getRoles()), Func.toStr(user.getAvatar(), TokenUtil.DEFAULT_AVATAR),
				user.getAccount(), AuthConstant.ENCRYPT + user.getPassword(), detail, true, true, true, true,
				AuthorityUtils.commaSeparatedStringToAuthorityList(Func.join(result.getData().getRoles())));
		} else {
			throw new InvalidGrantException("social grant failure, feign client return error");
		}

		// 组装认证数据，关闭密码校验
		Authentication userAuth = new UsernamePasswordAuthenticationToken(szykUserDetails, null, szykUserDetails.getAuthorities());
		((AbstractAuthenticationToken) userAuth).setDetails(parameters);
		OAuth2Request storedOAuth2Request = getRequestFactory().createOAuth2Request(client, tokenRequest);

		// 返回 OAuth2Authentication
		return new OAuth2Authentication(storedOAuth2Request, userAuth);
	}

}
