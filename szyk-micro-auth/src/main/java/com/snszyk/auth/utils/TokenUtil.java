/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.auth.utils;

import com.snszyk.common.constant.TenantConstant;
import com.snszyk.core.launch.constant.TokenConstant;
import com.snszyk.core.secure.TokenInfo;
import com.snszyk.core.secure.utils.SecureUtil;
import com.snszyk.core.tenant.SzykTenantProperties;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.jackson.JsonUtil;
import com.snszyk.core.tool.support.Kv;
import com.snszyk.core.tool.utils.*;
import com.snszyk.system.entity.Tenant;
import com.snszyk.system.user.entity.User;
import com.snszyk.system.user.entity.UserInfo;
import lombok.SneakyThrows;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.oauth2.common.exceptions.UnapprovedClientAuthenticationException;
import org.springframework.security.oauth2.common.exceptions.UserDeniedAuthorizationException;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 认证工具类
 *
 * <AUTHOR>
 */
public class TokenUtil {

	public final static String AVATAR = TokenConstant.AVATAR;
	public final static String ACCOUNT = TokenConstant.ACCOUNT;
	public final static String USER_NAME = TokenConstant.USER_NAME;
	public final static String NICK_NAME = TokenConstant.NICK_NAME;
	public final static String REAL_NAME = TokenConstant.REAL_NAME;
	public final static String USER_ID = TokenConstant.USER_ID;
	public final static String DEPT_ID = TokenConstant.DEPT_ID;
	public final static String POST_ID = TokenConstant.POST_ID;
	public final static String ROLE_ID = TokenConstant.ROLE_ID;
	public final static String ROLE_NAME = TokenConstant.ROLE_NAME;
	public final static String TENANT_ID = TokenConstant.TENANT_ID;
	public final static String OAUTH_ID = TokenConstant.OAUTH_ID;
	public final static String CLIENT_ID = TokenConstant.CLIENT_ID;
	public final static String DETAIL = TokenConstant.DETAIL;
	public final static String LICENSE = TokenConstant.LICENSE;
	public final static String LICENSE_NAME = TokenConstant.LICENSE_NAME;

	public final static String DEPT_HEADER_KEY = "Dept-Id";
	public final static String ROLE_HEADER_KEY = "Role-Id";
	public final static String CAPTCHA_HEADER_KEY = "Captcha-Key";
	public final static String CAPTCHA_HEADER_CODE = "Captcha-Code";
	public final static String CAPTCHA_NOT_CORRECT = "验证码不正确";
	public final static String AUTH_CODE_NOT_CORRECT = "钉钉授权码不正确";
	public final static String TENANT_HEADER_KEY = "Tenant-Id";
	public final static String TENANT_PARAM_KEY = "tenant_id";
	public final static String DEFAULT_TENANT_ID = "000000";
	public final static String TENANT_NOT_FOUND = "租户ID未找到";
	public final static String USER_TYPE_HEADER_KEY = "User-Type";
	public final static String DEFAULT_USER_TYPE = "web";
	public final static String TOKEN_NOT_PERMISSION = "令牌授权已过期";
	public final static String USER_NOT_FOUND = "用户名或密码错误";
	public final static String USER_HAS_NO_ROLE = "未获得用户的角色信息";
	public final static String USER_HAS_NO_TENANT = "未获得用户的租户信息";
	public final static String USER_HAS_NO_TENANT_PERMISSION = "租户授权已过期,请联系管理员";
	public final static String USER_HAS_TOO_MANY_FAILS = "登录错误次数过多,请稍后再试";
	public final static String HEADER_KEY = "Authorization";
	public final static String HEADER_PREFIX = "Basic ";
	public final static String DEFAULT_AVATAR = "";
	public final static String PASSWORD_KEY = "password";
	public final static String GRANT_TYPE_KEY = "grant_type";
	public final static String REFRESH_TOKEN_KEY = "refresh_token";
	public final static String USER_PERMISSION_EXPIRE_DATE = "账号授权已过期,请联系管理员";
	public final static String USER_PERMISSION_IS_DISABLED = "当前账号已禁用,请联系管理员";
	public final static String PHONE_HAS_NO_USER = "未获得当前手机号的用户信息";
	/**
	 * 统一认证授权码不可为空
	 */
	public final static String SSO_CODE_NOT_NULL = "统一认证授权码不可为空";
	/**
	 * 获取统一认证token异常
	 */
	public final static String GET_SSO_TOKEN_ERROR = "获取统一认证token异常";
	/**
	 * 统一认证获取用户信息地址不可为空
	 */
	public final static String USER_INFO_REQUEST_URL = "统一认证获取用户信息地址不可为空";
	/**
	 * 获取统一认证用户信息异常
	 */
	public final static String GET_SSO_USER_INFO_ERROR = "获取统一认证用户信息异常";

	private static SzykTenantProperties tenantProperties;

	/**
	 * 获取租户配置
	 *
	 * @return tenantProperties
	 */
	private static SzykTenantProperties getTenantProperties() {
		if (tenantProperties == null) {
			tenantProperties = SpringUtil.getBean(SzykTenantProperties.class);
		}
		return tenantProperties;
	}

	/**
	 * 解码
	 */
	@SneakyThrows
	public static String[] extractAndDecodeHeader() {
		String header = WebUtil.getRequest().getHeader(TokenUtil.HEADER_KEY);
		if (header == null || !header.startsWith(TokenUtil.HEADER_PREFIX)) {
			throw new UnapprovedClientAuthenticationException("请求头中无client信息");
		}

		byte[] base64Token = header.substring(6).getBytes(Charsets.UTF_8_NAME);

		byte[] decoded;
		try {
			decoded = Base64.getDecoder().decode(base64Token);
		} catch (IllegalArgumentException var7) {
			throw new BadCredentialsException("Failed to decode basic authentication token");
		}

		String token = new String(decoded, Charsets.UTF_8_NAME);
		int index = token.indexOf(StringPool.COLON);
		if (index == -1) {
			throw new BadCredentialsException("Invalid basic authentication token");
		} else {
			return new String[]{token.substring(0, index), token.substring(index + 1)};
		}
	}

	/**
	 * 获取请求头中的客户端id
	 */
	public static String getClientIdFromHeader() {
		String[] tokens = extractAndDecodeHeader();
		return tokens[0];
	}

	/**
	 * 获取token过期时间(次日凌晨3点)
	 *
	 * @return expire
	 */
	public static int getTokenValiditySecond() {
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.DAY_OF_YEAR, 1);
		cal.set(Calendar.HOUR_OF_DAY, 3);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.MILLISECOND, 0);
		return (int) (cal.getTimeInMillis() - System.currentTimeMillis()) / 1000;
	}

	/**
	 * 获取refreshToken过期时间
	 *
	 * @return expire
	 */
	public static int getRefreshTokenValiditySeconds() {
		return 60 * 60 * 24 * 15;
	}

	/**
	 * 判断租户权限
	 *
	 * @param tenant 租户信息
	 * @return boolean
	 */
	public static boolean judgeTenant(Tenant tenant) {
		if (tenant == null || tenant.getId() == null) {
			throw new UserDeniedAuthorizationException(TokenUtil.USER_HAS_NO_TENANT);
		}
		if (StringUtil.equalsIgnoreCase(tenant.getTenantId(), SzykConstant.ADMIN_TENANT_ID)) {
			return false;
		}
		Date expireTime = tenant.getExpireTime();
		if (getTenantProperties().getLicense()) {
			String licenseKey = tenant.getLicenseKey();
			String decrypt = DesUtil.decryptFormHex(licenseKey, TenantConstant.DES_KEY);
			expireTime = JsonUtil.parse(decrypt, Tenant.class).getExpireTime();
		}
		if (expireTime != null && expireTime.before(DateUtil.now())) {
			throw new UserDeniedAuthorizationException(TokenUtil.USER_HAS_NO_TENANT_PERMISSION);
		}
		return false;
	}

	/**
	 * 创建认证token
	 *
	 * @param userInfo 用户信息
	 * @return token
	 */
	public static Kv createAuthInfo(UserInfo userInfo) {
		Kv authInfo = Kv.create();
		User user = userInfo.getUser();
		// 设置jwt参数
		Map<String, Object> param = new HashMap<>(16);
		// param.put(TokenConstant.TOKEN_TYPE, TokenConstant.ACCESS_TOKEN);
		param.put(TokenConstant.TENANT_ID, user.getTenantId());
		param.put(TokenConstant.USER_ID, Func.toStr(user.getId()));
		param.put(TokenConstant.DEPT_ID, user.getDeptId());
		// param.put("unitId",userInfo.getUnitId());
		param.put(TokenConstant.POST_ID, user.getPostId());
		param.put(TokenConstant.ROLE_ID, user.getRoleId());
		param.put(TokenConstant.OAUTH_ID, userInfo.getOauthId());
		param.put(TokenConstant.ACCOUNT, user.getAccount());
		param.put(TokenConstant.USER_NAME, user.getAccount());
		param.put(TokenConstant.NICK_NAME, user.getRealName());
		param.put(TokenConstant.REAL_NAME, user.getName());
		param.put(TokenConstant.ROLE_NAME, Func.join(userInfo.getRoles()));
		param.put(TokenConstant.DETAIL, userInfo.getDetail());
		param.put("scope", new ArrayList<>().add("all"));
		param.put(TokenConstant.LICENSE, TokenConstant.LICENSE_NAME);
		param.put(TokenConstant.AVATAR, Func.toStr(user.getAvatar(), TokenConstant.DEFAULT_AVATAR));
		param.put("authorities", new ArrayList<>().add(Func.join(userInfo.getRoles())));

		// 拼装accessToken
		try {
			TokenInfo accessToken = SecureUtil.createJWT(param, "audience", "issuer", TokenConstant.ACCESS_TOKEN);
			// 返回accessToken
			return authInfo.set(TokenConstant.TENANT_ID, user.getTenantId())
				.set(TokenConstant.USER_ID, Func.toStr(user.getId()))
				.set(TokenConstant.DEPT_ID, user.getDeptId())
				.set(TokenConstant.POST_ID, user.getPostId())
				.set(TokenConstant.ROLE_ID, user.getRoleId())
				//.set("unitId",userInfo.getUnitId())
				.set(TokenConstant.OAUTH_ID, userInfo.getOauthId())
				.set(TokenConstant.ACCOUNT, user.getAccount())
				.set(TokenConstant.USER_NAME, user.getAccount())
				.set(TokenConstant.NICK_NAME, user.getRealName())
				.set(TokenConstant.REAL_NAME, user.getName())
				.set(TokenConstant.ROLE_NAME, Func.join(userInfo.getRoles()))
				.set(TokenConstant.AVATAR, Func.toStr(user.getAvatar(), TokenConstant.DEFAULT_AVATAR))
				.set(TokenConstant.ACCESS_TOKEN, accessToken.getToken())
				.set(TokenConstant.REFRESH_TOKEN, createRefreshToken(userInfo).getToken())
				.set(TokenConstant.TOKEN_TYPE, TokenConstant.BEARER)
				.set(TokenConstant.EXPIRES_IN, accessToken.getExpire())
				.set(TokenConstant.DETAIL, userInfo.getDetail())
				.set(TokenConstant.LICENSE, TokenConstant.LICENSE_NAME);
		} catch (Exception ex) {
			return authInfo.set("error_code", HttpServletResponse.SC_UNAUTHORIZED).set("error_description", ex.getMessage());
		}
	}

	/**
	 * 创建refreshToken
	 *
	 * @param userInfo 用户信息
	 * @return refreshToken
	 */
	private static TokenInfo createRefreshToken(UserInfo userInfo) {
		User user = userInfo.getUser();
		Map<String, Object> param = new HashMap<>(16);
		param.put(TokenConstant.TOKEN_TYPE, TokenConstant.REFRESH_TOKEN);
		param.put(TokenConstant.TENANT_ID, user.getTenantId());
		param.put(TokenConstant.USER_ID, Func.toStr(user.getId()));
		param.put(TokenConstant.DEPT_ID, Func.toStr(user.getDeptId()));
		param.put(TokenConstant.ROLE_ID, Func.toStr(user.getRoleId()));
		param.put(TokenConstant.POST_ID, user.getPostId());
		param.put(TokenConstant.OAUTH_ID, userInfo.getOauthId());
		param.put(TokenConstant.ACCOUNT, user.getAccount());
		param.put(TokenConstant.USER_NAME, user.getAccount());
		param.put(TokenConstant.REAL_NAME, user.getRealName());
		param.put(TokenConstant.NICK_NAME, user.getName());
		param.put(TokenConstant.ROLE_NAME, Func.join(userInfo.getRoles()));
		param.put(TokenConstant.AVATAR, Func.toStr(user.getAvatar(), TokenConstant.DEFAULT_AVATAR));
		param.put(TokenConstant.TOKEN_TYPE, TokenConstant.BEARER);
		param.put(TokenConstant.DETAIL, userInfo.getDetail());
		param.put(TokenConstant.LICENSE, TokenConstant.LICENSE_NAME);
		return SecureUtil.createJWT(param, "audience", "issuer", TokenConstant.REFRESH_TOKEN);
	}

}
