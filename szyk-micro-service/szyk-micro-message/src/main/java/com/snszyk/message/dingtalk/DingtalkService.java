package com.snszyk.message.dingtalk;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiGettokenRequest;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request;
import com.dingtalk.api.request.OapiMessageCorpconversationRecallRequest;
import com.dingtalk.api.request.OapiV2UserGetbymobileRequest;
import com.dingtalk.api.response.OapiGettokenResponse;
import com.dingtalk.api.response.OapiMessageCorpconversationAsyncsendV2Response;
import com.dingtalk.api.response.OapiMessageCorpconversationRecallResponse;
import com.dingtalk.api.response.OapiV2UserGetbymobileResponse;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.message.enums.DingTalkTypeEnum;
import com.snszyk.message.enums.NoticeNextOperationEnum;
import com.snszyk.system.user.entity.User;
import com.snszyk.system.user.feign.IUserClient;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 钉钉发送通知服务
 */
@Slf4j
@Service
public class DingtalkService {

	private static final String REDIS_DINGTALK_TOKEN_PREFIX = "dingding:accessToken:";

	@Autowired
	private SzykRedis redisService;

	@Value("${dingtalk.appkey}")
	private String appkey;

	@Value("${dingtalk.appsecret}")
	private String appsecret;


	@Value("${dingtalk.agentId}")
	private Long agentId;

	@Value("${dingtalk.snAddressPrrfix}")
	private String snAddressPrrfix;

	@Autowired
	private IUserClient userClient;

	/**
	 * 获取token
	 *
	 * @return
	 */
	public String getToken() {
		String redisToken = redisService.get(REDIS_DINGTALK_TOKEN_PREFIX + appkey);
		if (StringUtil.isNotBlank(redisToken)) {
			return redisToken;
		}
		DingTalkClient client = new DefaultDingTalkClient(DingtalkConstant.TOKEN_URL);
		OapiGettokenRequest request = new OapiGettokenRequest();
		request.setAppkey(appkey);
		request.setAppsecret(appsecret);
		request.setHttpMethod("GET");
		try {
			OapiGettokenResponse response = client.execute(request);
			if (!response.isSuccess()) {
				log.error(response.getErrmsg());
				return null;
			}
			String accessToken = response.getAccessToken();
			redisService.setEx(REDIS_DINGTALK_TOKEN_PREFIX + appkey, accessToken, 110 * 60L);
			return accessToken;
		} catch (ApiException e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 发送待办和提醒 卡片方式
	 *
	 * @param userIdList
	 * @param content
	 * @param
	 * @return
	 */
	public Long asyncsendV2(List<String> userIdList, DingTalkTypeEnum type, String content, String url, NoticeNextOperationEnum operationEnum) {
		try {
			DingTalkClient client = new DefaultDingTalkClient(DingtalkConstant.SEND_MESSAGE_URL);
			OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
			request.setAgentId(agentId);
			request.setUseridList(userIdList.stream().collect(Collectors.joining(",")));
			request.setToAllUser(false);
			OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
			//卡片通知
			msg.setMsgtype("action_card");
			msg.setActionCard(new OapiMessageCorpconversationAsyncsendV2Request.ActionCard());
			msg.getActionCard().setTitle(type.getDesc());
			msg.getActionCard().setMarkdown("<font color=\"#00a7ff\">" + type.getDesc() + "</font><br />" +
				" \n" +
				content
			);
			if (operationEnum == null) {
				operationEnum = NoticeNextOperationEnum.VIEW_DETAIL;
			}
			msg.getActionCard().setSingleTitle(operationEnum.getDesc());
			//msg.getActionCard().setSingleUrl("dingtalk://dingtalkclient/page/link?url=" +
			//	URLEncoder.encode(snAddressPrrfix + url) +
			//	"&pc_slide=false");
			msg.getActionCard().setSingleUrl(url);
			request.setMsg(msg);
			String accessToken = this.getToken();
			if (StringUtil.isBlank(accessToken)) {
				log.error("token获取失败,钉钉消息发送失败!");
				return null;
			}
			try {
				OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(request, accessToken);
				if (!rsp.isSuccess()) {
					log.error(rsp.getErrmsg());
					return null;
				}
				return rsp.getTaskId();
			} catch (ApiException e) {
				log.error(e.getMessage(), e);
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return null;
	}
	/**
	 * 发送markdown通知
	 *
	 * @param userIdList
	 * @param content
	 * @param
	 * @return
	 */
	public Long asyncSendNotice(List<String> userIdList, String title, String content) {
		log.info("钉钉消息发送用户id：==============={}", userIdList);
		try {
			DingTalkClient client = new DefaultDingTalkClient(DingtalkConstant.SEND_MESSAGE_URL);
			OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
			request.setAgentId(agentId);
			request.setUseridList(userIdList.stream().collect(Collectors.joining(",")));
			request.setToAllUser(false);
			OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
			//卡片通知
			msg.setMsgtype("markdown");
			msg.setMarkdown(new OapiMessageCorpconversationAsyncsendV2Request.Markdown());
			msg.getMarkdown().setTitle(title);
			msg.getMarkdown().setText("<font color=\"#00a7ff\">" + title + "</font><br />" +
				" \n" +
				content
			);
			request.setMsg(msg);
			String accessToken = this.getToken();
			if (StringUtil.isBlank(accessToken)) {
				log.error("token获取失败,钉钉消息发送失败!");
				return null;
			}
			try {
				OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(request, accessToken);
				if (!rsp.isSuccess()) {
					log.error(rsp.getErrmsg());
					return null;
				}
				return rsp.getTaskId();
			} catch (ApiException e) {
				log.error(e.getMessage(), e);
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return null;
	}

	/**
	 * 根据手机号获取userId
	 *
	 * @param phone
	 * @return
	 */
	public String getUserIdByMobile(String phone) {
		DingTalkClient client = new DefaultDingTalkClient(DingtalkConstant.GET_USER_ID_URL);
		OapiV2UserGetbymobileRequest req = new OapiV2UserGetbymobileRequest();
		req.setMobile(phone);
		String accessToken = this.getToken();
		if (StringUtil.isBlank(accessToken)) {
			log.error("token获取失败,钉钉消息发送失败!");
			return null;
		}
		try {
			OapiV2UserGetbymobileResponse oapiV2UserGetbymobileResponse = client.execute(req, accessToken);
			if (!oapiV2UserGetbymobileResponse.isSuccess()) {
				log.error(oapiV2UserGetbymobileResponse.getErrmsg());
				return null;
			}
			OapiV2UserGetbymobileResponse.UserGetByMobileResponse result = oapiV2UserGetbymobileResponse.getResult();
			String userid = result.getUserid();
			return userid;
		} catch (ApiException e) {
			log.error(e.getMessage(), e);
		}
		return null;
	}

	/**
	 * 撤回消息通知(只能撤回24小时之内的)
	 *
	 * @param msgTaskId 钉钉消息id
	 * @return
	 */
	public Integer recall(Long msgTaskId) {
		DingTalkClient client = new DefaultDingTalkClient(DingtalkConstant.RE_CALL);
		OapiMessageCorpconversationRecallRequest req = new OapiMessageCorpconversationRecallRequest();
		req.setAgentId(agentId);
		req.setMsgTaskId(msgTaskId);
		String accessToken = this.getToken();
		OapiMessageCorpconversationRecallResponse rsp = null;
		try {
			rsp = client.execute(req, accessToken);
		} catch (ApiException e) {
			log.error(e.getMessage(), e);
		}
		if (!rsp.isSuccess()) {
			log.error(rsp.getErrmsg());
			return null;
		}
		return Func.toInt(StringPool.ONE);
	}

	/**
	 * 测试钉钉消息
	 *
	 * @return
	 */
	public Integer testSendMessage() {
//		ArrayList<String> list = new ArrayList<>();
//		list.add("172800133620028565");
//		asyncsendV2(list, DingTalkTypeEnum.TODO_TASK, "我是内容测试", "https://www.baidu.com", NoticeNextOperationEnum.TO_EXAMINE);
		return Func.toInt(StringPool.ONE);
	}

	/**
	 * 根据系统的用户id查询钉钉的userid
	 *
	 * @param userIdList
	 * @return
	 */
	public List<String> getDingTalkUserIdsByUserIds(String userIdList) {
		if(Func.isEmpty(userIdList)){
			return null;
		}
		List<Long> userIds = Func.toLongList(userIdList);
		ArrayList<String> resultList = new ArrayList<>();
		R<List<User>> userList = userClient.userInfoByIds(userIds);
		if (CollectionUtil.isEmpty(userList.getData())) {
			log.error("获取钉钉用户id失败!userIds:" + userIds);
		}
		List<User> users = userList.getData();
		for (User user : users) {
			String dingtalkUserId = user.getDingtalkUserId();
			if (StringUtil.isBlank(dingtalkUserId)) {
				String phone = user.getPhone();
				if (StringUtil.isBlank(phone)) {
					log.error("获取钉钉用户id失败!用户未配置手机号,userid:" + user.getId());
				}
				dingtalkUserId = getUserIdByMobile(phone);
				if (StringUtil.isBlank(dingtalkUserId)) {
					log.error("获取钉钉端userId失败,userid:" + user.getId());
				}
				R r = userClient.updateDingTalkUserId(user.getId(), dingtalkUserId);
				if (!r.isSuccess()) {
					log.error("设置用户的钉钉userId失败! userId:" + user.getId() + ",钉钉的userId:" + dingtalkUserId);
				}
			}
			if (StringUtil.isNotBlank(dingtalkUserId)) {
				resultList.add(dingtalkUserId);
			}
		}
		return resultList;
		// TODO: 2023/5/10  没有钉钉userid的没处理
	}
}
