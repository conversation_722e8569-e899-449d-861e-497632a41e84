package com.snszyk.message.feign;

import com.snszyk.core.tenant.annotation.NonDS;
import com.snszyk.core.tool.api.R;
import com.snszyk.message.service.logic.MessageLogicService;
import com.snszyk.message.vo.MessageTaskVO;
import com.snszyk.message.vo.MessageVo;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 消息feign客户端
 * <AUTHOR>
 */
@NonDS
@RestController
@AllArgsConstructor
public class MessageClient implements IMessageClient{

	private MessageLogicService messageLogicService;

	@Override
	@PostMapping(PUSH_MESSAGE)
	public R pushMessage(MessageVo message) {
		return messageLogicService.commitMessage(message);
	}

	@Override
	@PostMapping(PUSH_BATCH_MESSAGE)
	public R pushBatchMessage(List<MessageVo> messageList) {
		return null;
	}

	@Override
	@PostMapping(HANDLE_MESSAGE_TASK)
	public R handleMessageTask(MessageTaskVO messageTask) {
		return R.status(messageLogicService.handleTask(messageTask));
	}

}
