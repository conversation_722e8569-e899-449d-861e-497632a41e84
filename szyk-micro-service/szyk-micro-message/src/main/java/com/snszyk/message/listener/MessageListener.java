package com.snszyk.message.listener;

import com.snszyk.common.constant.AssetConstant;
import com.snszyk.message.service.logic.MessageLogicService;
import com.snszyk.message.vo.MessageVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * 消息中心监听Listener
 *
 * <AUTHOR>
 * @date 2024/07/30 15:16
 **/
@Slf4j
@Configuration
public class MessageListener {

	@Resource
	private MessageLogicService messageLogicService;

	//@RabbitHandler
	//@RabbitListener(queues = AssetConstant.Rabbit.QUEUE_ASSET_MESSAGE)
	public void messageHandler(MessageVo message) {
		log.info("=====》开始推送系统消息：========{}", message);
		messageLogicService.commitMessage(message);
		log.info("=====》完成推送系统消息：========{}", message);
	}

}
