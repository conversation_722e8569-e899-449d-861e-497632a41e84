package com.snszyk.message.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.message.dto.MessageDto;
import com.snszyk.message.entity.Message;
import com.snszyk.message.vo.MessageVo;

/**
 * IMessageService
 *
 * <AUTHOR> Baomingming
 * @create 2022/8/1
 */
public interface IMessageService extends BaseService<Message> {

	/**
	 * 消息发送分页列表
	 *
	 * @param page page
	 * @param vo vo
	 * @return
	 */
	IPage<MessageDto> pageSend(IPage<MessageDto> page, MessageVo vo);

}
