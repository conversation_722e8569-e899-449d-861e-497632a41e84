/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.system.user.service;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.core.mp.support.Query;
import com.snszyk.system.user.entity.User;
import com.snszyk.system.user.entity.UserInfo;
import com.snszyk.system.user.entity.UserOauth;
import com.snszyk.system.user.enums.UserEnum;
import com.snszyk.system.user.excel.UserExcel;
import com.snszyk.system.user.vo.UserVO;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 */
public interface IUserService extends BaseService<User> {

	/**
	 * 新增用户
	 *
	 * @param user
	 * @return
	 */
	boolean submit(User user);

	/**
	 * 修改用户
	 *
	 * @param user
	 * @return
	 */
	boolean updateUser(User user);

	/**
	 * 修改用户基本信息
	 *
	 * @param user
	 * @return
	 */
	boolean updateUserInfo(User user);

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param user
	 * @param deptId
	 * @param tenantId
	 * @return
	 */
	IPage<User> page(IPage<User> page, User user, Long deptId, String tenantId);

	/**
	 * 配置部门用户分页
	 *
	 * @param page
	 * @param user
	 * @param deptId
	 * @param tenantId
	 * @return
	 */
	IPage<User> selectUserPage(IPage<User> page, User user, Long deptId, String tenantId);

	/**
	 * 自定义带权限分页
	 *
	 * @param page
	 * @param user
	 * @param deptId
	 * @param tenantId
	 * @return
	 */
	IPage<User> selectUserPageOauth(IPage<User> page, User user, Long deptId, String tenantId);

	/**
	 * 自定义分页
	 *
	 * @param user
	 * @param query
	 * @return
	 */
	IPage<UserVO> selectUserSearch(UserVO user, Query query);


	/**
	 * 用户信息
	 *
	 * @param userId
	 * @return
	 */
	UserInfo userInfo(Long userId);

	/**
	 * 用户信息
	 *
	 * @param tenantId
	 * @param account
	 * @return
	 */
	UserInfo userInfo(String tenantId, String account);

	/**
	 * 用户信息
	 *
	 * @param dingTalkId
	 * @return
	 */
	UserInfo userInfo(String dingTalkId);

	/**
	 * 用户信息
	 *
	 * @param tenantId
	 * @param account
	 * @param userEnum
	 * @return
	 */
	UserInfo userInfo(String tenantId, String account, UserEnum userEnum);

	/**
	 * 用户信息
	 *
	 * @param userOauth
	 * @return
	 */
	UserInfo userInfo(UserOauth userOauth);

	/**
	 * 根据账号获取用户
	 *
	 * @param tenantId
	 * @param account
	 * @return
	 */
	User userByAccount(String tenantId, String account);

	/**
	 * 给用户设置角色
	 *
	 * @param userIds
	 * @param roleIds
	 * @return
	 */
	boolean grant(String userIds, String roleIds);

	/**
	 * 初始化密码
	 *
	 * @param userIds
	 * @return
	 */
	boolean initPassword(String userIds);

	/**
	 * 重置密码
	 *
	 * @param user
	 * @return
	 */
	boolean resetPassword(User user);

	/**
	 * 修改密码
	 *
	 * @param userId
	 * @param oldPassword
	 * @param newPassword
	 * @param newPassword1
	 * @return
	 */
	boolean updatePassword(Long userId, String oldPassword, String newPassword, String newPassword1);

	/**
	 * 删除用户
	 *
	 * @param userIds
	 * @return
	 */
	boolean removeUser(String userIds);

	/**
	 * 导入用户数据
	 *
	 * @param data
	 * @param isCovered
	 * @return
	 */
	void importUser(List<UserExcel> data, Boolean isCovered);

	/**
	 * 导出用户数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<UserExcel> exportUser(Wrapper<User> queryWrapper);

	/**
	 * 注册用户
	 *
	 * @param user
	 * @param oauthId
	 * @return
	 */
	boolean registerGuest(User user, Long oauthId);

	/**
	 * 配置用户平台
	 *
	 * @param userId
	 * @param userType
	 * @param userExt
	 * @return
	 */
	boolean updatePlatform(Long userId, Integer userType, String userExt);

	/**
	 * 用户详细信息
	 *
	 * @param user
	 * @return
	 */
	UserVO platformDetail(User user);

	/**
	 * 供应商用户分页
	 *
	 * @param page
	 * @param user
	 * @param tenantId
	 * @return
	 */
	IPage<User> supplierUserPage(IPage<User> page, User user, String tenantId);

	/**
	 * 新增供应商用户
	 *
	 * @param user
	 * @return
	 */
	boolean submitSupplierUser(User user);

	/**
	 * 品牌商用户分页
	 *
	 * @param page
	 * @param user
	 * @param tenantId
	 * @return
	 */
	IPage<User> brandUserPage(IPage<User> page, User user, String tenantId);

	/**
	 * 新增品牌商用户
	 *
	 * @param user
	 * @return
	 */
	boolean submitBrandUser(User user);

	/**
	 * 选择人员分页
	 *
	 * @param page
	 * @param user
	 * @return
	 */
	IPage<User> selectPage(IPage<User> page, UserVO user);

	/**
	 * 选择用户
	 *
	 * @param userIdList
	 * @return
	 */
	boolean select(List<Long> userIdList);

	/**
	 * 取消选择用户
	 *
	 * @param id
	 * @return
	 */
	boolean cancelSelect(Long id);

}
