<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.snszyk</groupId>
        <artifactId>szyk-micro-service</artifactId>
        <version>1.3.0.RELEASE</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>szyk-micro-desk</artifactId>
    <name>${project.artifactId}</name>
    <version>${szyk.project.version}</version>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-core-boot</artifactId>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-starter-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-micro-desk-api</artifactId>
            <version>${szyk.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-micro-system-api</artifactId>
            <version>${szyk.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-micro-user-api</artifactId>
            <version>${szyk.project.version}</version>
        </dependency>
        <!--Oss-->
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-starter-oss</artifactId>
        </dependency>
        <!--MinIO-->
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
        </dependency>
        <!--QiNiu-->
        <dependency>
            <groupId>com.qiniu</groupId>
            <artifactId>qiniu-java-sdk</artifactId>
        </dependency>
        <!--<dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-starter-transaction</artifactId>
        </dependency>-->
    </dependencies>

    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.3.10.RELEASE</version>
                <configuration>
                    <mainClass>com.snszyk.desk.DeskApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
