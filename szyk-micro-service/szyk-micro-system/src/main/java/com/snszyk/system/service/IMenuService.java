/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.tool.node.TreeNode;
import com.snszyk.core.tool.support.Kv;
import com.snszyk.system.entity.Menu;
import com.snszyk.system.vo.MenuVO;

import java.util.List;
import java.util.Map;

/**
 * 服务类
 *
 * <AUTHOR>
 */
public interface IMenuService extends IService<Menu> {

	/**
	 * 懒加载列表
	 *
	 * @param parentId
	 * @param param
	 * @param type 菜单终端类型：PC、APP
	 * @return
	 */
	List<MenuVO> lazyList(Long parentId, Map<String, Object> param, String type);

	/**
	 * 懒加载菜单列表
	 *
	 * @param parentId
	 * @param param
	 * @param type 菜单终端类型：PC、APP
	 * @return
	 */
	List<MenuVO> lazyMenuList(Long parentId, Map<String, Object> param, String type);

	/**
	 * 菜单树形结构
	 *
	 * @param roleId
	 * @param topMenuId
	 * @param type 菜单终端类型：PC、APP
	 * @return
	 */
	List<MenuVO> routes(String roleId, Long topMenuId, String type);

	/**
	 * 按钮树形结构
	 *
	 * @param roleId
	 * @param type 菜单终端类型：PC、APP、PORTAL
	 * @return
	 */
	List<MenuVO> buttons(String roleId, String type);

	/**
	 * 树形结构
	 *
	 * @param type 菜单终端类型：PC、APP
	 * @return
	 */
	List<TreeNode> tree(String type);

	/**
	 * 授权树形结构
	 *
	 * @param user
	 * @return
	 */
	List<TreeNode> grantTree(SzykUser user);

	/**
	 * 顶部菜单树形结构
	 *
	 * @param user
	 * @param type 菜单终端类型：PC、APP
	 * @return
	 */
	List<TreeNode> grantTopTree(SzykUser user, String type);

	/**
	 * 数据权限授权树形结构
	 *
	 * @param user
	 * @return
	 */
	List<TreeNode> grantDataScopeTree(SzykUser user);

	/**
	 * 接口权限授权树形结构
	 *
	 * @param user
	 * @return
	 */
	List<TreeNode> grantApiScopeTree(SzykUser user);

	/**
	 * 默认选中节点
	 *
	 * @param roleIds
	 * @return
	 */
	List<String> roleTreeKeys(String roleIds);

	/**
	 * 默认选中节点
	 *
	 * @param topMenuIds
	 * @return
	 */
	List<String> topTreeKeys(String topMenuIds);

	/**
	 * 默认选中节点
	 *
	 * @param roleIds
	 * @return
	 */
	List<String> dataScopeTreeKeys(String roleIds);

	/**
	 * 默认选中节点
	 *
	 * @param roleIds
	 * @return
	 */
	List<String> apiScopeTreeKeys(String roleIds);

	/**
	 * 获取配置的角色权限
	 *
	 * @param user
	 * @return
	 */
	List<Kv> authRoutes(SzykUser user);

	/**
	 * 删除菜单
	 *
	 * @param ids
	 * @return
	 */
	boolean removeMenu(String ids);

	/**
	 * 提交
	 *
	 * @param menu
	 * @return
	 */
	boolean submit(Menu menu);


	/**
	 * 递归获取子菜单id
	 * @param parentIds
	 * @return
	 */
	List<Long> listChildMenusRecursively(List<Long> parentIds);

}
