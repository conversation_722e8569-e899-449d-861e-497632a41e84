/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.cache.utils.CacheUtil;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.secure.annotation.PreAuth;
import com.snszyk.core.secure.constant.AuthConstant;
import com.snszyk.core.tenant.annotation.NonDS;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.constant.RoleConstant;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.support.Kv;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.system.cache.DictCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.enums.DictEnum;
import com.snszyk.system.service.IDeptService;
import com.snszyk.system.user.cache.UserCache;
import com.snszyk.system.user.entity.User;
import com.snszyk.system.vo.DelResultVO;
import com.snszyk.system.vo.DeptVO;
import com.snszyk.system.wrapper.DeptWrapper;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import static com.snszyk.core.cache.constant.CacheConstant.SYS_CACHE;

/**
 * 控制器
 *
 * <AUTHOR>
 */
@NonDS
@RestController
@AllArgsConstructor
@RequestMapping("/dept")
@Api(value = "部门", tags = "部门")
//@PreAuth("hasAnyRole('administrator', 'admin', 'isc_admin')")
public class DeptController extends SzykController {

	private final IDeptService deptService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入dept")
	public R<DeptVO> detail(Dept dept) {
		Dept detail = deptService.getOne(Condition.getQueryWrapper(dept));
		return R.data(DeptWrapper.build().entityVO(detail));
	}

	/**
	 * 列表
	 */
	@GetMapping("/list")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "deptName", value = "部门名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "fullName", value = "部门全称", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "列表", notes = "传入dept")
	public R<List<DeptVO>> list(@ApiIgnore @RequestParam Map<String, Object> dept, SzykUser szykUser) {
		QueryWrapper<Dept> queryWrapper = Condition.getQueryWrapper(dept, Dept.class);
		List<Dept> list = deptService.list((!szykUser.getTenantId().equals(SzykConstant.ADMIN_TENANT_ID)) ? queryWrapper.lambda().eq(Dept::getTenantId, szykUser.getTenantId()) : queryWrapper);
		return R.data(DeptWrapper.build().listNodeVO(list));
	}

	/**
	 * 懒加载列表
	 */
	@GetMapping("/lazy-list")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "deptName", value = "部门名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "deptCode", value = "部门代码", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "懒加载列表", notes = "传入dept")
	public R<List<DeptVO>> lazyList(@ApiIgnore @RequestParam Map<String, Object> dept, Long parentId, SzykUser szykUser) {
		dept.put("roleId", (szykUser == null) ? null : Func.toLong(szykUser.getRoleId()));
		List<DeptVO> list = deptService.lazyList(szykUser.getTenantId(), parentId, dept);
		return R.data(DeptWrapper.build().listNodeLazyVO(list));
	}

	/**
	 * 获取部门树形结构
	 *
	 * @return
	 */
	@GetMapping("/tree")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "树形结构", notes = "树形结构")
	public R<List<DeptVO>> tree(String tenantId, SzykUser szykUser) {
		List<DeptVO> tree = deptService.tree(Func.toStrWithEmpty(tenantId, szykUser.getTenantId()));
		return R.data(tree);
	}

	/**
	 * 懒加载获取部门树形结构
	 */
	@GetMapping("/lazy-tree")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "懒加载树形结构", notes = "树形结构")
	public R<List<DeptVO>> lazyTree(String tenantId, Long parentId, SzykUser szykUser) {
		List<DeptVO> tree = deptService.lazyTree(Func.toStrWithEmpty(tenantId, szykUser.getTenantId()), parentId);
		return R.data(tree);
	}

	/**
	 * 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入dept")
	public R submit(@Valid @RequestBody Dept dept) {
		if (deptService.submit(dept)) {
			CacheUtil.clear(SYS_CACHE);
			// 返回懒加载树更新节点所需字段
			Kv kv = Kv.create().set("id", String.valueOf(dept.getId())).set("tenantId", dept.getTenantId())
				.set("deptCategoryName", DictCache.getValue(DictEnum.ORG_CATEGORY, dept.getDeptCategory()));
			return R.data(kv);
		}
		return R.fail("操作失败");
	}

	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		CacheUtil.clear(SYS_CACHE);
		return R.status(deptService.removeDept(ids));
	}

	/**
	 * 校验并删除
	 */
	@PostMapping("/check-remove")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "校验并删除", notes = "传入ids")
	public R<DelResultVO> checkAndRemove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		DelResultVO delResultVO = deptService.checkAndRemoveDept(Func.toLongList(ids));
		R<DelResultVO> result = new R<>();
		result.setCode(ResultCode.SUCCESS.getCode());
		result.setData(delResultVO);
		result.setSuccess(delResultVO.getFailureNumber() == 0);
		return result;
	}

	/**
	 * 下拉数据源
	 */
	@GetMapping("/select")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "下拉数据源", notes = "传入id集合")
	@PreAuth(AuthConstant.PERMIT_ALL)
	public R<List<Dept>> select(Long userId, String deptId) {
		if (Func.isNotEmpty(userId)) {
			User user = UserCache.getUser(userId);
			deptId = user.getDeptId();
		}
		List<Dept> list = deptService.list(Wrappers.<Dept>lambdaQuery().in(Dept::getId, Func.toLongList(deptId)));
		return R.data(list);
	}

	/**
	 * 部门信息查询
	 */
	@GetMapping("/searchDept")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "部门信息查询", notes = "传入deptName或者parentId")
	@PreAuth(AuthConstant.PERMIT_ALL)
	public R<List<DeptVO>> searchDept(String deptName, Long parentId) {
		return R.data(deptService.search(deptName, parentId));
	}

	/**
	 * 选择部门列表
	 */
	@GetMapping("/select-lazy-list")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "deptName", value = "部门名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "deptCode", value = "部门代码", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "选择部门列表", notes = "传入dept")
	@PreAuth(AuthConstant.PERMIT_ALL)
	public R<List<DeptVO>> selectLazyList(@ApiIgnore @RequestParam Map<String, Object> dept, Long parentId, SzykUser szykUser) {
		dept.put("roleId", (szykUser == null) ? null : Func.toLong(szykUser.getRoleId()));
		List<DeptVO> list = deptService.selectLazyList(szykUser.getTenantId(), parentId, dept);
		return R.data(DeptWrapper.build().listNodeLazyVO(list));
	}

	/**
	 * 获取部门树形结构
	 *
	 * @return
	 */
	@GetMapping("/select-tree")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "树形结构", notes = "树形结构")
	@PreAuth(AuthConstant.PERMIT_ALL)
	public R<List<DeptVO>> selectTree(String tenantId, SzykUser szykUser) {
		List<DeptVO> tree = deptService.selectTree(Func.toStrWithEmpty(tenantId, szykUser.getTenantId()));
		return R.data(tree);
	}

	/**
	 * 可见
	 */
	@PostMapping("/select")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "选择部门", notes = "传入id")
	public R select(Long id) {
		return R.status(deptService.select(id));
	}

	/**
	 * 取消选择
	 */
	@PostMapping("/cancelSelect")
	@ApiOperationSupport(order = 14)
	@ApiOperation(value = "取消选择部门", notes = "传入id")
	public R cancelSelect(Long id) {
		return R.status(deptService.cancelSelect(id));
	}

	/**
	 * 基本搜索树
	 */
	@GetMapping("/searchTree")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "deptName", value = "组织结构名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "deptCode", value = "组织机构代码", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 15)
	@ApiOperation(value = "基本搜索树", notes = "传入basicTree")
	@PreAuth(AuthConstant.PERMIT_ALL)
	public R<List<DeptVO>> searchTree(@ApiIgnore @RequestParam Map<String, Object> dept, SzykUser szykUser) {
		List<Dept> list = deptService.getNodeParent(szykUser.getTenantId(), dept);
		return R.data(DeptWrapper.build().listNodeVO(list));
	}

	/**
	 * 选择搜索树
	 */
	@GetMapping("/selectSearchTree")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "deptName", value = "组织结构名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "deptCode", value = "组织机构代码", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 16)
	@ApiOperation(value = "选择搜索树", notes = "传入basicTree")
	@PreAuth(AuthConstant.PERMIT_ALL)
	public R<List<DeptVO>> selectSearchTree(@ApiIgnore @RequestParam Map<String, Object> dept, SzykUser szykUser) {
		dept.put("isSelected", 1);
		List<Dept> list = deptService.getNodeParent(szykUser.getTenantId(), dept);
		return R.data(DeptWrapper.build().listNodeVO(list));
	}


}
