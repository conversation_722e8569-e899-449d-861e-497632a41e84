<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.system.mapper.RoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="roleResultMap" type="com.snszyk.system.entity.Role">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="role_name" property="roleName"/>
        <result column="sort" property="sort"/>
        <result column="role_alias" property="roleAlias"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="treeNodeResultMap" type="com.snszyk.core.tool.node.TreeNode">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="title" property="title"/>
        <result column="value" property="value"/>
        <result column="key" property="key"/>
    </resultMap>

    <select id="selectRolePage" resultMap="roleResultMap">
        select * from szyk_role where is_deleted = 0
    </select>

    <select id="tree" resultMap="treeNodeResultMap">
        select id, parent_id, role_name as title, id as "value", id as "key" from szyk_role where is_deleted = 0
        <if test="param1!=null">
            and tenant_id = #{param1}
        </if>
        <if test="param2!=null">
            and role_alias &lt;&gt; #{param2}
        </if>
    </select>

    <select id="getRoleNames" resultType="java.lang.String">
        SELECT
        role_name
        FROM
        szyk_role
        WHERE
        id IN
        <foreach collection="array" item="ids" index="index" open="(" close=")" separator=",">
            #{ids}
        </foreach>
        and is_deleted = 0
    </select>

    <select id="getRoleAliases" resultType="java.lang.String">
        SELECT
        role_alias
        FROM
        szyk_role
        WHERE
        id IN
        <foreach collection="array" item="ids" index="index" open="(" close=")" separator=",">
            #{ids}
        </foreach>
        and is_deleted = 0
    </select>

    <!--根据角色别名获取角色详情-->
    <select id="selectByRoleAlias" resultType="com.snszyk.system.vo.RoleVO">
        SELECT *
        FROM szyk_role
        WHERE is_deleted = 0 and tenant_id = #{param1} and role_alias = #{param2}
    </select>

    <!--根据角色别名获取角色详情-->
    <select id="selectListByRoleAlias" resultType="com.snszyk.system.vo.RoleVO">
        SELECT *
        FROM szyk_role
        WHERE is_deleted = 0 and tenant_id = #{param1} and role_alias like concat('%',#{param2},'%')
    </select>

    <!--根据角色别名获取角色详情-->
    <select id="selectByRoleAliases" resultType="com.snszyk.system.vo.RoleVO">
        SELECT *
        FROM szyk_role
        WHERE is_deleted = 0 and tenant_id = #{param1} and role_alias in
        <foreach collection="param2" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!--根据用户id获取角色列表-->
    <select id="selectByUserId" resultType="com.snszyk.system.vo.RoleVO">
        SELECT
            r.*
        FROM
            szyk_role r
                LEFT JOIN szyk_user_role u ON r.id = u.role_id
        WHERE
            r.is_deleted = 0
          AND u.user_id = #{userId}
    </select>

</mapper>
