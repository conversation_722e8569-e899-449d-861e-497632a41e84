/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.system.entity.RoleMenu;
import com.snszyk.system.vo.RoleMenuVO;

import java.util.List;

/**
 * Mapper 接口
 *
 * <AUTHOR>
 */
public interface RoleMenuMapper extends BaseMapper<RoleMenu> {

	/**
	 * 自定义分页
	 * @param page
	 * @param roleMenu
	 * @return
	 */
	List<RoleMenuVO> selectRoleMenuPage(IPage page, RoleMenuVO roleMenu);

}
