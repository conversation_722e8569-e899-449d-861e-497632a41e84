package com.snszyk.system.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseEntity;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.system.dto.OrderDTO;
import com.snszyk.system.dto.OrderImportDTO;
import com.snszyk.system.entity.Order;
import com.snszyk.system.excel.OrderExcel;
import com.snszyk.system.excel.OrderImportFailExcel;
import com.snszyk.system.mapper.OrderMapper;
import com.snszyk.system.service.IOrderService;
import com.snszyk.system.vo.OrderVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 订单服务实现类
 * <AUTHOR>
 */
@AllArgsConstructor
@Service
public class OrderServiceImpl extends BaseServiceImpl<OrderMapper, Order> implements IOrderService {

	private final SzykRedis szykRedis;

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param order
	 * @return
	 */
	@Override
	public IPage<OrderVO> selectOrderPage(IPage<OrderVO> page, OrderVO order) {
		return page.setRecords(baseMapper.selectOrderPage(page, order));
	}

	/**
	 * 导入订单 - 返回导入结果信息
	 * @param file excel文件
	 * @param isCovered 是否覆盖导入
	 * @return
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public OrderImportDTO importOrder(MultipartFile file, Boolean isCovered) {
		OrderImportDTO result = new OrderImportDTO();
		result.setSuccessCount(0);
		result.setFailCount(0);
		List<OrderImportFailExcel> failReasonList = new ArrayList<>();

		//解析Excel
		List<OrderDTO> orderDtoList;
		try {
			List<OrderExcel> orderExcelList = EasyExcel.read(file.getInputStream()).head(OrderExcel.class).sheet().doReadSync();
			orderDtoList = BeanUtil.copyProperties(orderExcelList, OrderDTO.class);
		} catch (IOException e) {
			e.printStackTrace();
			throw new ServiceException("Excel文件解析失败！错误信息 = " + e.getMessage());
		}

		//保存or更新数据
		if (CollectionUtil.isNotEmpty(orderDtoList)) {
			orderDtoList.forEach(orderDto -> {
				Order order = Objects.requireNonNull(BeanUtil.copy(orderDto, Order.class));

				//校验数据（仅以 支付方式是否为1-4 为例给出导入失败原因）
				if (order.getPaymentType() == null || order.getPaymentType() < 1 || order.getPaymentType() > 4) {
					result.setFailCount(result.getFailCount() + 1);
					OrderImportFailExcel importFailExcel = BeanUtil.copyProperties(order, OrderImportFailExcel.class);
					importFailExcel.setFailReason("支付方式必须为【银行卡、支付宝、微信、其他】中的一个！");
					failReasonList.add(importFailExcel);
					return;
				}

				//查询订单是否存在 - 根据订单编号
				LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
				queryWrapper.eq(Order::getNum, orderDto.getNum())
					.eq(BaseEntity::getIsDeleted, SzykConstant.DB_NOT_DELETED);
				Order existedOrder = baseMapper.selectOne(queryWrapper);
				if (existedOrder != null) {
					//覆盖数据
					if (isCovered) {
						order.setId(existedOrder.getId());
						baseMapper.updateById(order);
						result.setSuccessCount(result.getSuccessCount() + 1);
					} else {
						result.setFailCount(result.getFailCount() + 1);
						OrderImportFailExcel importFailExcel = BeanUtil.copyProperties(order, OrderImportFailExcel.class);
						importFailExcel.setFailReason("已存在相同订单编号的订单！");
						failReasonList.add(importFailExcel);
					}
					return;
				}

				//插入数据库，记录错误原因
				try {
					baseMapper.insert(order);
					result.setSuccessCount(result.getSuccessCount() + 1);
				} catch (Exception e) {
					e.printStackTrace();
					result.setFailCount(result.getFailCount() + 1);
					OrderImportFailExcel importFailExcel = BeanUtil.copyProperties(order, OrderImportFailExcel.class);
					importFailExcel.setFailReason("插入数据库失败，错误信息 = " + e.getMessage());
					failReasonList.add(importFailExcel);
				}
			});
		}

		// 缓存导入失败的订单数据和原因
		if (result.getFailCount() > 0 && failReasonList.size() > 0) {
			String failReasonKey = IdWorker.get32UUID();
			szykRedis.setEx(failReasonKey, failReasonList, Duration.ofDays(1L));
			result.setFailReasonDownloadKey(failReasonKey);
		}

		return result;
	}

	/**
	 * 导出订单
	 * @param order 过滤条件
	 * @return
	 */
	@Override
	public List<OrderExcel> exportOrder(Map<String, Object> order) {
		QueryWrapper<Order> queryWrapper = Condition.getQueryWrapper(order, Order.class);
		queryWrapper.lambda().eq(Order::getIsDeleted, SzykConstant.DB_NOT_DELETED)
			.orderByDesc(Order::getOrderTime);
		List<Order> orderList = baseMapper.selectList(queryWrapper);
		return BeanUtil.copyProperties(orderList, OrderExcel.class);
	}
}
