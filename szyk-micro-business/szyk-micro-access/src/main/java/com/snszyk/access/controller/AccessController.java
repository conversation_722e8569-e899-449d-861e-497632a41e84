package com.snszyk.access.controller;

import com.alibaba.fastjson.JSONObject;
import com.snszyk.access.conf.AccessConfig;
import com.snszyk.access.entity.AccessInfo;
import com.snszyk.access.face.RequestDeptMessage;
import com.snszyk.access.face.RequestSupplierMessage;
import com.snszyk.access.face.RequestUserMessage;
import com.snszyk.access.face.ResponseMessage;
import com.snszyk.access.service.impl.AccessInfoService;
import com.snszyk.access.service.logic.BusinessLogic;
import com.snszyk.core.boot.ctrl.SzykController;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping("/access")
@Api(value = "对接协议 ", tags = "信息")
public class AccessController extends SzykController {

	private final AccessConfig accessConfig;

	private final BusinessLogic businessLogic;
	@RequestMapping("userInfo")
	public ResponseMessage updateUserInfo(@RequestHeader("usercode")String usercode, @RequestHeader("password")String password, @RequestBody RequestUserMessage message){

		if(!accessConfig.validate(usercode, password)){
			return new ResponseMessage(message.getBatchId(), 0, null);
		}
		return businessLogic.businessDisposeUser(message);
	}


	@RequestMapping("deptInfo")
	public ResponseMessage updateDeptInfo(@RequestHeader("usercode")String usercode, @RequestHeader("password")String password, @RequestBody RequestDeptMessage message){
		if(!accessConfig.validate(usercode, password)){
			return new ResponseMessage(message.getBatchId(), 0, null);
		}
		return this.businessLogic.businessDisposeDept(message);
	}

	@RequestMapping("supplierInfo")
	public ResponseMessage updateSupplierInfo(@RequestHeader("usercode")String usercode, @RequestHeader("password")String password, @RequestBody RequestSupplierMessage message){
		System.out.println(JSONObject.toJSONString(message));
		if(!accessConfig.validate(usercode, password)){
			return new ResponseMessage(message.getBatchId(), 0, null);
		}
		return this.businessLogic.businessDisposeSupplier(message);
	}
}
