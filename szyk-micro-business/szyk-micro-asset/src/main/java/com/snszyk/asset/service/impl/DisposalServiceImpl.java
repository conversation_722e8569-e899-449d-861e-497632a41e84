/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.asset.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.asset.dto.AssetDTO;
import com.snszyk.asset.entity.Asset;
import com.snszyk.asset.entity.Disposal;
import com.snszyk.asset.enums.AssetStatusEnum;
import com.snszyk.asset.enums.BizStatusEnum;
import com.snszyk.asset.mapper.AssetMapper;
import com.snszyk.asset.mapper.DisposalMapper;
import com.snszyk.asset.service.IDisposalService;
import com.snszyk.asset.vo.DisposalVO;
import com.snszyk.asset.wrapper.AssetWrapper;
import com.snszyk.asset.wrapper.DisposalWrapper;
import com.snszyk.common.constant.AssetConstant;
import com.snszyk.common.utils.BizCodeUtil;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.resource.feign.IAttachClient;
import com.snszyk.resource.vo.AttachVO;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Role;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 资产处置单表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@AllArgsConstructor
@Service
public class DisposalServiceImpl extends BaseServiceImpl<DisposalMapper, Disposal> implements IDisposalService {

	private final AssetMapper assetMapper;
	private final IAttachClient attachClient;

	@Override
	public IPage<DisposalVO> page(IPage<DisposalVO> page, DisposalVO vo) {
		Long iscAdmin = null;
		Role role = SysCache.getRole(AuthUtil.getTenantId(), AssetConstant.Asset.ISC_ADMIN);
		if (Func.isNotEmpty(role)) {
			iscAdmin = role.getId();
		}
		// 数据权限
		Long roleId = vo.getRoleId();
		if(Func.isNotEmpty(roleId)){
			// 除系统管理员外只有处置人看自己的单据
			if(!Func.equals(iscAdmin, roleId)){
				vo.setOperateUser(AuthUtil.getUserId());
			}
		}
		if (Func.isNotEmpty(vo.getStartDate())) {
			vo.setStartDate(vo.getStartDate() + " 00:00:00");
		}
		if (Func.isNotEmpty(vo.getEndDate())) {
			vo.setEndDate(vo.getEndDate() + " 23:59:59");
		}
		List<Disposal> list = baseMapper.page(page, vo);
		List<DisposalVO> records = null;
		if(Func.isNotEmpty(list)){
			records = DisposalWrapper.build().listVO(list);
			records.forEach(record -> {
				Integer cnt = assetMapper.selectCount(Wrappers.<Asset>query().lambda()
					.in(Asset::getId, Func.toLongList(record.getAssetId())));
				record.setAssetCount(cnt);
			});
		}
		return page.setRecords(records);
	}

	@Override
	public DisposalVO detail(Long id) {
		Disposal disposal = this.getById(id);
		if(disposal == null){
			throw new ServiceException(ResultCode.FAILURE);
		}
		DisposalVO detail = DisposalWrapper.build().entityVO(this.getById(id));
		// 图片
		if(Func.isNotEmpty(detail.getImageId())){
			R<List<AttachVO>> attachListR = attachClient.listByIds(Func.toLongList(detail.getImageId()));
			if(attachListR.isSuccess() && Func.isNotEmpty(attachListR.getData())){
				detail.setImageList(attachListR.getData());
			}
		}
		// 附件
		if(Func.isNotEmpty(detail.getAttachId())){
			R<List<AttachVO>> attachListR = attachClient.listByIds(Func.toLongList(detail.getAttachId()));
			if(attachListR.isSuccess() && Func.isNotEmpty(attachListR.getData())){
				detail.setAttachList(attachListR.getData());
			}
		}
		// 资产列表
		List<AssetDTO> assetList = new ArrayList<>();
		if(BizStatusEnum.DRAFT == BizStatusEnum.getByCode(detail.getStatus())){
			List<Asset> list = assetMapper.selectList(Wrappers.<Asset>query().lambda()
				.in(Asset::getId, Func.toLongList(detail.getAssetId())));
			if(Func.isNotEmpty(list)){
				assetList = AssetWrapper.build().listDTO(list);
			}
		} else {
			List<Asset> list = assetMapper.getAssetsByBusinessId(id);
			if(Func.isNotEmpty(list)){
				assetList = AssetWrapper.build().listDTO(list);
			}
		}
		detail.setAssetList(assetList);
		return detail;
	}

	@Override
	public String add(DisposalVO vo) {
		Disposal disposal = Objects.requireNonNull(BeanUtil.copy(vo, Disposal.class));
		disposal.setNo(BizCodeUtil.generate("CZD"))
			.setOperateDept(Func.toLong(AuthUtil.getDeptId())).setStatus(BizStatusEnum.DRAFT.getCode());
		this.save(disposal);
		return disposal.getNo();
	}

	@Override
	public String modify(DisposalVO vo) {
		Disposal disposal = this.getById(vo.getId());
		if(disposal == null){
			throw new ServiceException(ResultCode.FAILURE);
		}
		BeanUtil.copy(vo, disposal);
		this.updateById(disposal);
		return disposal.getNo();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public String submit(DisposalVO vo) {
		Disposal disposal = Objects.requireNonNull(BeanUtil.copy(vo, Disposal.class));
		if(Func.isEmpty(disposal.getId())){
			disposal.setNo(BizCodeUtil.generate("CZD")).setOperateDept(Func.toLong(AuthUtil.getDeptId()));
		}
		disposal.setStatus(BizStatusEnum.DONE.getCode());
		this.saveOrUpdate(disposal);
		// 更新资产信息
		if(Func.isNotEmpty(vo.getAssetId())){
			List<Asset> assetList = assetMapper.selectList(Wrappers.<Asset>query().lambda()
				.in(Asset::getId, Func.toLongList(vo.getAssetId())));
			assetList.forEach(asset -> {
				asset.setDisposalId(disposal.getId());
				asset.setStatus(AssetStatusEnum.DISPOSED.getCode());
				assetMapper.updateById(asset);
			});
		}
		return disposal.getNo();
	}
}
