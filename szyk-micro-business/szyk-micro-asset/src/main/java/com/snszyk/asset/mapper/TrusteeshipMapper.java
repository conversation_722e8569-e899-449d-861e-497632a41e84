/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.asset.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.asset.entity.Trusteeship;
import com.snszyk.asset.vo.TrusteeshipVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资产托管申请表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-11-05
 */
public interface TrusteeshipMapper extends BaseMapper<Trusteeship> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param trusteeship
	 * @return
	 */
	List<Trusteeship> page(IPage page, @Param("trusteeship") TrusteeshipVO trusteeship);


}
