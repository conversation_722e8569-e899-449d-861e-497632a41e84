<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.asset.mapper.DeptCostCenterMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="deptCostCenterResultMap" type="com.snszyk.asset.entity.DeptCostCenter">
        <id column="id" property="id"/>
        <result column="dept_id" property="deptId"/>
        <result column="dept_code" property="deptCode"/>
        <result column="cost_center_code" property="costCenterCode"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
    </resultMap>


</mapper>
