/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.asset.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.asset.service.IRepairRecordService;
import com.snszyk.asset.vo.RepairRecordVO;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;

/**
 * 资产维修记录表 控制器
 *
 * <AUTHOR>
 * @since 2024-11-19
 */
@RestController
@AllArgsConstructor
@RequestMapping("/repair-record")
@Api(value = "资产维修记录表", tags = "资产维修记录表接口")
public class RepairRecordController extends SzykController {

	private final IRepairRecordService repairRecordService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入repairRecord")
	public R<RepairRecordVO> detail(Long id) {
		return R.data(repairRecordService.detail(id));
	}

	/**
	 * 分页 资产维修记录表
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "no", value = "维修单号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "status", value = "状态", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "assetCode", value = "资产编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "startDate", value = "开始时间", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "结束时间", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入repairRecord")
	public R<IPage<RepairRecordVO>> page(@ApiIgnore RepairRecordVO repairRecord, Query query, SzykUser user) {
		repairRecord.setRoleId(user == null ? null : Func.toLong(user.getRoleId()));
		IPage<RepairRecordVO> pages = repairRecordService.page(Condition.getPage(query), repairRecord);
		return R.data(pages);
	}

	/**
	 * 暂存 资产维修记录表
	 */
	@PostMapping("/add")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "暂存", notes = "传入repairRecord")
	public R add(@Valid @RequestBody RepairRecordVO repairRecord) {
		return R.status(repairRecordService.add(repairRecord));
	}

	/**
	 * 提交 资产维修记录表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "提交", notes = "传入repairRecord")
	public R submit(@Valid @RequestBody RepairRecordVO repairRecord) {
		return R.status(repairRecordService.submit(repairRecord));
	}


	/**
	 * 删除 资产维修记录表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(repairRecordService.removeByIds(Func.toLongList(ids)));
	}

	/**
	 * 绑定资产 资产维修记录表
	 */
	@PostMapping("/bindAsset")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "绑定资产", notes = "传入ids")
	public R bindAsset(@RequestBody RepairRecordVO repairRecord) {
		return R.status(repairRecordService.bindAsset(repairRecord));
	}

	/**
	 * 根据资产编号获取最新的维修记录 资产盘点单表
	 */
	@GetMapping("/getLastRepairByAsset")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "根据资产编号获取最新的维修记录", notes = "传入assetCode")
	public R<RepairRecordVO> getLastRepairByAsset(@ApiParam(value = "资产编号", required = true) @RequestParam String assetCode) {
		return R.data(repairRecordService.getLastRepairByAsset(assetCode));
	}


}
