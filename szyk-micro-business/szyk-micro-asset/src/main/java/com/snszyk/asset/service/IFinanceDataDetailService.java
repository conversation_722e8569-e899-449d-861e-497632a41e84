/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.asset.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.asset.dto.FixedAssetDTO;
import com.snszyk.asset.entity.FinanceDataDetail;
import com.snszyk.asset.vo.FinanceDataDetailVO;

/**
 * 财务资产数据明细表 服务类
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
public interface IFinanceDataDetailService extends IService<FinanceDataDetail> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param vo
	 * @return
	 */
	IPage<FixedAssetDTO> page(IPage<FixedAssetDTO> page, FinanceDataDetailVO vo);


}
