/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.asset.wrapper;

import com.snszyk.asset.entity.Supplier;
import com.snszyk.asset.vo.SupplierVO;
import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.enums.DictBizEnum;
import com.snszyk.system.user.cache.UserCache;
import com.snszyk.system.user.entity.User;

import java.util.Objects;

/**
 * 供应商表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
public class SupplierWrapper extends BaseEntityWrapper<Supplier, SupplierVO> {

	public static SupplierWrapper build() {
		return new SupplierWrapper();
 	}

	@Override
	public SupplierVO entityVO(Supplier supplier) {
		SupplierVO supplierVO = Objects.requireNonNull(BeanUtil.copy(supplier, SupplierVO.class));
		String typeName = DictBizCache.getValue(DictBizEnum.SUPPLIER_TYPE, supplier.getType());
		supplierVO.setTypeName(typeName);
		User createUser = UserCache.getUser(supplier.getCreateUser());
		if(Func.isNotEmpty(createUser)){
			supplierVO.setCreateUserName(createUser.getRealName());
		}
		User updateUser = UserCache.getUser(supplier.getUpdateUser());
		if(Func.isNotEmpty(updateUser)){
			supplierVO.setUpdateUserName(updateUser.getRealName());
		}
		return supplierVO;
	}

}
