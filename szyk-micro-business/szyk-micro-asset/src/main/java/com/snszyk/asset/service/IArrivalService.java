/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.asset.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.asset.dto.ArrivalImportDTO;
import com.snszyk.asset.entity.Arrival;
import com.snszyk.asset.vo.ArrivalVO;
import com.snszyk.core.mp.base.BaseService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 资产到货单表 服务类
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
public interface IArrivalService extends BaseService<Arrival> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param arrival
	 * @return
	 */
	IPage<ArrivalVO> page(IPage<ArrivalVO> page, ArrivalVO arrival);

	/**
	 * 详情
	 *
	 * @param no
	 * @return
	 */
	ArrivalVO detail(String no);

	/**
	 * 暂存
	 *
	 * @param arrival
	 * @return
	 */
	boolean add(ArrivalVO arrival);

	/**
	 * 提交
	 *
	 * @param arrival
	 * @return
	 */
	boolean submit(ArrivalVO arrival);

	/**
	 * 回退
	 *
	 * @param no
	 * @return
	 */
	boolean cancel(String no);

	/**
	 * 导入到货单数据
	 *
	 * @param data
	 * @param isCovered
	 * @return
	 */
	boolean importArrival(List<ArrivalImportDTO> data, Boolean isCovered);

	/**
	 * 下载导入失败到货单数据
	 *
	 * @param response
	 */
	void downloadFailExcel(HttpServletResponse response);


}
