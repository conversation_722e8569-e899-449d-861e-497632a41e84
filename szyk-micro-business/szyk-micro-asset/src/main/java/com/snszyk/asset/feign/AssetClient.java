/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.asset.feign;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.asset.entity.*;
import com.snszyk.asset.service.*;
import com.snszyk.asset.vo.LocationVO;
import com.snszyk.core.tenant.annotation.NonDS;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Objects;

/**
 * 资产管理Feign实现类
 *
 * <AUTHOR>
 */
@NonDS
@ApiIgnore
@RestController
@AllArgsConstructor
public class AssetClient implements IAssetClient {

	private final ICategoryService categoryService;
	private final ICategoryMcService categoryMcService;
	private final ILocationService locationService;
	private final ISupplierService supplierService;
	private final IBrandService brandService;

	@Override
	public R submitSupplier(Supplier supplier) {
		return R.status(supplierService.saveOrUpdate(supplier));
	}

	@Override
	public R<Supplier> getSupplierByCode(String code) {
		return R.data(supplierService.getOne(Wrappers.<Supplier>query().lambda().eq(Supplier::getNo, code)));
	}

	@Override
	@GetMapping(CATEGORY)
	public R<Category> getCategory(String code) {
		return R.data(categoryService.getOne(Wrappers.<Category>query().lambda().eq(Category::getCode, code)));
	}

	@Override
	@GetMapping(CATEGORY_MC)
	public R<CategoryMc> getCategoryMc(String code) {
		return R.data(categoryMcService.getOne(Wrappers.<CategoryMc>query().lambda()
			.eq(CategoryMc::getClassifyNo, code)));
	}

	@Override
	@GetMapping(LOCATION)
	public R<Location> getLocation(Long id) {
		return R.data(locationService.getById(id));
	}

	@Override
	@GetMapping(SUPPLIER)
	public R<Supplier> getSupplier(Long id) {
		return R.data(supplierService.getById(id));
	}

	@Override
	@GetMapping(BRAND)
	public R<Brand> getBrand(Long id) {
		return R.data(brandService.getById(id));
	}

	@Override
	@PostMapping(ADD_TOP_LOCATION)
	public R addLocation(LocationVO vo) {
		Location location = Objects.requireNonNull(BeanUtil.copy(vo, Location.class));
		location.setId(Func.toLong(vo.getTenantId()));
		return R.status(locationService.save(location));
	}

}
