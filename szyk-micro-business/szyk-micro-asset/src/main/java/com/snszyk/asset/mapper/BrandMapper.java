/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.asset.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.asset.entity.Brand;
import com.snszyk.asset.vo.BrandVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资产品牌表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
public interface BrandMapper extends BaseMapper<Brand> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param brand
	 * @return
	 */
	List<Brand> page(IPage page, @Param("brand") BrandVO brand);

}
