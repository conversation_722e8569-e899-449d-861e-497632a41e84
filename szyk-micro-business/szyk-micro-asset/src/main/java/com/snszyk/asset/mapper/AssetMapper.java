/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.asset.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.asset.dto.AssetDTO;
import com.snszyk.asset.entity.Asset;
import com.snszyk.asset.vo.AssetVO;
import com.snszyk.asset.vo.PrintVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 资产档案表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
public interface AssetMapper extends BaseMapper<Asset> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param asset
	 * @return
	 */
	List<Asset> page(IPage page, @Param("asset") AssetVO asset);

	/**
	 * 列表导出
	 *
	 * @param asset
	 * @return
	 */
	List<Asset> listExport(@Param("asset") AssetVO asset);

	/**
	 * 根据业务ID单查询资产
	 *
	 * @param businessId
	 * @return
	 */
	List<Asset> getAssetsByBusinessId(@Param("businessId") Long businessId);

	Integer print(PrintVO printVO);

	/**
	 * 生成资产编号
	 *
	 * @param selfCode
	 * @return
	 */
	Integer generateAssetCode(@Param("selfCode") String selfCode);

	AssetDTO selectDetail(Long id);

	/**
	 * 根据编码或名称查询详情
	 */
	AssetDTO selectDetailByCode(@Param("key") String key);

	/**
	 * 资产一般统计
	 *
	 * @param asset
	 * @return
	 */
	List<Map<String, Object>> assetCommonStat(@Param("asset") AssetVO asset);

	/**
	 * 地点资产统计
	 *
	 * @param asset
	 * @return
	 */
	List<Map<String, Object>> assetLocStat(@Param("asset") AssetVO asset);

	/**
	 * 部门资产统计
	 *
	 * @param asset
	 * @return
	 */
	List<Map<String, Object>> assetDeptStat(@Param("asset") AssetVO asset);

	/**
	 * 资产分类统计
	 *
	 * @param asset
	 * @return
	 */
	List<Map<String, Object>> assetCategoryStat(@Param("asset") AssetVO asset);

	/**
	 * 资产状态统计
	 *
	 * @param asset
	 * @return
	 */
	List<Map<String, Object>> assetStatusStat(@Param("asset") AssetVO asset);

	/**
	 * 入库管理选择资产
	 *
	 * @param page
	 * @param asset
	 * @return
	 */
	List<AssetDTO> assetPageForStock(IPage page, @Param("asset") AssetVO asset);

	/**
	 * 本月入库资产数
	 *
	 * @param asset
	 * @return
	 */
	Integer assetsMonthIn(@Param("asset") AssetVO asset);

	/**
	 * 本月出库资产数
	 *
	 * @param asset
	 * @return
	 */
	Integer assetsMonthOut(@Param("asset") AssetVO asset);

	/**
	 * 根据临时编号删除待入库的资产
	 *
	 * @param tempCodeList
	 * @return
	 */
	Integer removeByTempCode(@Param("list") List<String> tempCodeList);

	/**
	 * 重复的资产编号
	 *
	 * @return
	 */
	List<String> selectDuplicateSn();

	/**
	 * 根据呈批件ID查询资产分页
	 *
	 * @param page
	 * @param approvalId
	 * @return
	 */
	List<Asset> assetPageByApprovalId(IPage page, @Param("approvalId") Long approvalId);


}
