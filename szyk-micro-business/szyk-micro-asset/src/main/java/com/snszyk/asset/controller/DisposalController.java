/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.asset.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.asset.entity.Disposal;
import com.snszyk.asset.service.IDisposalService;
import com.snszyk.asset.vo.DisposalVO;
import com.snszyk.asset.wrapper.DisposalWrapper;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;

/**
 * 资产处置单表 控制器
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@RestController
@AllArgsConstructor
@RequestMapping("/disposal")
@Api(value = "资产处置单表", tags = "资产处置单表接口")
public class DisposalController extends SzykController {

	private final IDisposalService disposalService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入id")
	public R<DisposalVO> detail(Long id) {
		return R.data(disposalService.detail(id));
	}

	/**
	 * 列表 资产处置单表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "列表", notes = "传入disposal")
	public R<IPage<DisposalVO>> list(Disposal disposal, Query query) {
		IPage<Disposal> pages = disposalService.page(Condition.getPage(query), Condition.getQueryWrapper(disposal));
		return R.data(DisposalWrapper.build().pageVO(pages));
	}

	/**
	 * 自定义分页 资产处置单表
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "no", value = "单号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "startDate", value = "开始时间", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "结束时间", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入disposal")
	public R<IPage<DisposalVO>> page(@ApiIgnore DisposalVO disposal, Query query, SzykUser user) {
		disposal.setRoleId(user == null ? null : Func.toLong(user.getRoleId()));
		return R.data(disposalService.page(Condition.getPage(query), disposal));
	}

	/**
	 * 新增 资产处置单表
	 */
	@PostMapping("/add")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入disposal")
	public R<String> add(@Valid @RequestBody DisposalVO disposal) {
		return R.data(disposalService.add(disposal));
	}

	/**
	 * 修改 资产处置单表
	 */
	@PostMapping("/modify")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入disposal")
	public R<String> modify(@Valid @RequestBody DisposalVO disposal) {
		return R.data(disposalService.modify(disposal));
	}

	/**
	 * 新增或修改 资产处置单表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入disposal")
	public R<String> submit(@Valid @RequestBody DisposalVO disposal) {
		return R.data(disposalService.submit(disposal));
	}


	/**
	 * 删除 资产处置单表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(disposalService.deleteLogic(Func.toLongList(ids)));
	}


}
