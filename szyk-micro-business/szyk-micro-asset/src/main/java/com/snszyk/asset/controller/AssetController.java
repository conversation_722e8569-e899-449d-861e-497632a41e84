/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.asset.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.asset.dto.ApprovalAssetDTO;
import com.snszyk.asset.dto.AssetDTO;
import com.snszyk.asset.dto.AssetFlowDTO;
import com.snszyk.asset.entity.Asset;
import com.snszyk.asset.entity.FinanceWbs;
import com.snszyk.asset.service.IAssetService;
import com.snszyk.asset.service.IFinanceWbsService;
import com.snszyk.asset.service.logic.BusinessService;
import com.snszyk.asset.service.logic.IUpdateLogService;
import com.snszyk.asset.vo.AssetVO;
import com.snszyk.asset.vo.DateUpdateLogVO;
import com.snszyk.asset.vo.PrintVO;
import com.snszyk.asset.vo.UpdateLogVO;
import com.snszyk.asset.wrapper.AssetWrapper;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 资产档案表 控制器
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/asset")
@Api(value = "资产档案表", tags = "资产档案表接口")
public class AssetController extends SzykController {

	private final IAssetService assetService;
	private final IFinanceWbsService financeWbsService;
	private final IUpdateLogService updateLogService;
	private final BusinessService businessService;

	/**
	 * 详情 资产档案表
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入id")
	public R<AssetDTO> detail(@ApiParam(value = "资产主键", required = true) @RequestParam Long id) {
		return R.data(assetService.detail(id));
	}

	/**
	 * 查看 资产档案表
	 */
	@GetMapping("/view")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "查看", notes = "传入id")
	public R<AssetDTO> view(@ApiParam(value = "资产主键", required = true) @RequestParam Long id) {
		return R.data(assetService.view(id));
	}

	/**
	 * 分页 资产档案表
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "keyword", value = "关键字", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "pattern", value = "资产类型（1：资产，2：低值易耗）", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "name", value = "资产名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "categoryCode", value = "资产分类", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "status", value = "资产状态", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "code", value = "资产编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "tempCode", value = "临时编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "maintenanceExpireDateStr", value = "维保到期日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "deviceType", value = "打印类型", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "model", value = "资产型号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "serialNumber", value = "SN编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "isPasted", value = "是否打印", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "measureUnit", value = "计量单位", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "useYears", value = "使用年限", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "storageUser", value = "入库人", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "storageDateStart", value = "入库日期-开始时间", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "storageDateEnd", value = "入库日期-结束时间", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "brandId", value = "资产品牌", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "locationId", value = "存放地点", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "userId", value = "使用人", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "invoicePriceStr", value = "发票价格", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "invoiceDateStr", value = "发票日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "assetSource", value = "资产来源", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "updateDateStr", value = "更新日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "financeCode", value = "财务资产编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "supplierId", value = "供应商", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "isInventory", value = "是否盘点（0：否，1：是）", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "cancelAuth", value = "是否取消权限（0：否，1：是）", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "statusStr", value = "多个状态逗号分隔", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "bindAssets", value = "只在绑定资产分页传1", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "sortParam", value = "点击搜索传1（不点搜索不传此参数）", paramType = "query", dataType = "Integer")
	})
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入asset")
	public R<IPage<AssetDTO>> page(@ApiIgnore AssetVO asset, Query query, SzykUser user) {
		asset.setRoleId(user == null ? null : Func.toLong(user.getRoleId()));
		IPage<AssetDTO> pages = assetService.page(Condition.getPage(query), asset);
		return R.data(pages);
	}

	/**
	 * 新增或修改 资产档案表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增或修改", notes = "传入asset")
	public R submit(@Valid @RequestBody AssetVO asset) {
		boolean flag = Boolean.FALSE;
		if(Func.isNotEmpty(asset.getId())){
			Asset entity = assetService.getById(asset.getId());
			if(Func.isNotEmpty(entity)){
				flag = assetService.saveUpdateLog(entity, asset, Func.firstLong(AuthUtil.getDeptId()), AuthUtil.getUserId());
			}
		} else {
			flag = Boolean.TRUE;
		}
		if(flag){
			return R.status(assetService.submit(asset));
		}
		return R.status(Boolean.FALSE);
	}

	/**
	 * 删除 资产档案表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(assetService.removeAsset(Func.toLongList(ids)));
	}

	/**
	 * 修改维保到期日期 资产档案表
	 */
	@PostMapping("/updateDate")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "修改维保到期日期", notes = "传入asset")
	public R updateDate(@Valid @RequestBody AssetVO asset) {
		return R.status(assetService.updateDate(asset, true));
	}

	/**
	 * 导出列表Excel 资产档案表
	 */
	@GetMapping("/exportExcel")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "keyword", value = "关键字", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "pattern", value = "资产类型（1：资产，2：低值易耗）", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "name", value = "资产名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "categoryCode", value = "资产分类", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "status", value = "资产状态", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "code", value = "资产编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "tempCode", value = "临时编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "maintenanceExpireDateStr", value = "维保到期日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "deviceType", value = "打印类型", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "model", value = "资产型号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "serialNumber", value = "SN编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "isPasted", value = "是否打印", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "measureUnit", value = "计量单位", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "useYears", value = "使用年限", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "storageUser", value = "入库人", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "storageDateStart", value = "入库日期-开始时间", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "storageDateEnd", value = "入库日期-结束时间", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "brandId", value = "资产品牌", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "locationId", value = "存放地点", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "userId", value = "使用人", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "invoicePriceStr", value = "发票价格", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "invoiceDateStr", value = "发票日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "assetSource", value = "资产来源", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "updateDateStr", value = "更新日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "financeCode", value = "财务资产编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "supplierId", value = "供应商", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "isInventory", value = "是否盘点（0：否，1：是）", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "cancelAuth", value = "是否取消权限（0：否，1：是）", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "statusStr", value = "多个状态逗号分隔", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "bindAssets", value = "只在绑定资产分页传1", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "sortParam", value = "点击搜索传1（不点搜索不传此参数）", paramType = "query", dataType = "Integer")
	})
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "导出列表Excel", notes = "传入asset")
	public void exportExcel(@ApiIgnore AssetVO asset, SzykUser user, HttpServletResponse response) {
		asset.setRoleId(user == null ? null : Func.toLong(user.getRoleId())).setPattern(1);
		assetService.exportExcel(asset, response);
		//return R.success("操作成功");
	}

	/**
	 * 分页 资产档案表
	 */
	@GetMapping("/userlist/{userId}")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "用户资产列表", notes = "用户资产列表")
	public R<List<AssetDTO>> userlist(@PathVariable("userId") Long userid) {
		List<Asset> list = assetService.lambdaQuery().eq(Asset::getUserId, userid).list();
		List<AssetDTO> data = new ArrayList<>();
		list.forEach(asset -> {
			data.add(AssetWrapper.build().entityDTO(asset));
		});
		return R.data(data);
	}

	/**
	 * 根据业务ID查询资产列表 资产档案表
	 */
	@GetMapping("/business/{id}")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "根据业务ID查询资产列表", notes = "传入业务ID")
	public R<List<AssetDTO>> assetListByBusinessId(@PathVariable("id") Long id) {
		List<AssetDTO> list = assetService.getAssetsByBusinessId(id);
		return R.data(list);
	}

	/**
	 * 根据呈批件ID查询资产列表 资产档案表
	 */
	@GetMapping("/approval-assets/{approvalId}")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "根据呈批件ID查询资产列表", notes = "传入呈批件ID")
	public R<List<ApprovalAssetDTO>> assetListByApprovalId(@PathVariable("approvalId") Long approvalId) {
		return R.data(assetService.assetListByApprovalId(approvalId));
	}

	/**
	 * 根据呈批件ID查询资产列表 资产档案表
	 */
	@GetMapping("/approval-assets/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "approvalId", value = "呈批件id", paramType = "query", dataType = "long")
	})
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "根据呈批件ID查询资产列表", notes = "传入asset")
	public R<IPage<ApprovalAssetDTO>> assetPageByApprovalId(@ApiIgnore AssetVO asset, Query query) {
		return R.data(assetService.assetPageByApprovalId(Condition.getPage(query), asset.getApprovalId()));
	}

	/**
	 * 修改呈批件或合同 资产档案表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "修改呈批件或合同", notes = "传入asset")
	public R update(@Valid @RequestBody AssetVO asset) {
		Asset entity = assetService.getById(asset.getId());
		if(entity == null){
			throw new ServiceException(String.format("当前资产%s不存在，请刷新后重试！", entity.getCode()));
		}
		if(Func.isNotEmpty(asset.getApprovalId())){
			entity.setApprovalId(asset.getApprovalId());
		}
		if(Func.isNotEmpty(asset.getContractId())){
			entity.setContractId(asset.getContractId());
		}
		return R.status(assetService.updateById(entity));
	}

	@PostMapping("/updateAssets")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "批量修改资产信息", notes = "传入assetList")
	public R updateAssets(@Valid @RequestBody List<AssetVO> assetList) {
		return R.status(assetService.updateBatch(assetList));
	}

	/**
	 * 维保日期操作记录 资产档案表
	 */
	@GetMapping("/dateUpdateLog")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "维保日期操作记录", notes = "传入assetId")
	public R<IPage<DateUpdateLogVO>> dateUpdateLog(Long assetId, Query query) {
		return R.data(assetService.dateUpdateLog(Condition.getPage(query), assetId));
	}

	/**
	 * 打印操作
	 */
	@PostMapping("/print")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "打印", notes = "传入print")
	public R print(@RequestBody PrintVO print) {
		return R.data(assetService.print(print));
	}

	@PostMapping("/bind")
	@ApiOperationSupport(order = 14)
	@ApiOperation(value = "绑定设备资产信息", notes = "传入ids")
	public R bind(@RequestBody AssetVO vo) {
		Asset asset = this.assetService.getById(vo.getId());
		asset.setRfid(vo.getRfid());
		return R.status(assetService.updateById(asset));
	}

	/**
	 * 资产流转记录
	 */
	@GetMapping("/flow/{id}")
	@ApiOperationSupport(order = 15)
	@ApiOperation(value = "资产流转记录", notes = "传入资产ID")
	public R<List<AssetFlowDTO>> assetFlow(@PathVariable("id") Long id) {
		AssetDTO dto = assetService.selectDetail(id);
		return R.data(this.businessService.businessList(dto,id));
	}

	/**
	 * 绑定呈批件或合同 资产档案表
	 */
	@PostMapping("/bindBusiness")
	@ApiOperationSupport(order = 16)
	@ApiOperation(value = "绑定呈批件或合同", notes = "传入inventory")
	public R bindBusiness(@Valid @RequestBody AssetVO asset) {
		return R.data(assetService.bindBusiness(asset));
	}

	/**
	 * 打印次数归零 资产档案表
	 */
	@PostMapping("/initializePrint")
	@ApiOperationSupport(order = 17)
	@ApiOperation(value = "打印次数归零", notes = "传入asset")
	public R initializePrint(@Valid @RequestBody AssetVO asset) {
		Asset entity = assetService.getById(asset.getId());
		if(entity == null){
			throw new ServiceException("当前资产不存在，请刷新后重试！");
		}
		entity.setPrint(0).setRfid(null);
		return R.data(assetService.updateById(entity));
	}

	/**
	 * 绑定RFID 资产档案表
	 */
	@PostMapping("/bindRfid")
	@ApiOperationSupport(order = 18)
	@ApiOperation(value = "绑定RFID", notes = "传入asset")
	public R bindRfid(@Valid @RequestBody AssetVO asset) {
		Asset entity = assetService.getById(asset.getId());
		if(entity == null){
			throw new ServiceException("当前资产不存在，请刷新后重试！");
		}
		if(Func.isEmpty(asset.getRfid())){
			entity.setRfid(null);
		} else {
			Asset a = assetService.getOne(Wrappers.<Asset>query().lambda().eq(Asset::getRfid, asset.getRfid()));
			if(Func.isNotEmpty(a)){
				throw new ServiceException(String.format("当前资产%s已绑定此RFID", a.getCode()));
			}
			entity.setRfid(asset.getRfid());
		}
		return R.data(assetService.updateById(entity));
	}

	/**
	 * 低值易耗分页 资产档案表
	 */
	@GetMapping("/lowValueConsumablePage")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "keyword", value = "关键字", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "pattern", value = "资产类型（1：资产，2：低值易耗）", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "name", value = "资产名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "categoryCode", value = "资产分类", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "status", value = "资产状态", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "code", value = "资产编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "tempCode", value = "临时编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "maintenanceExpireDateStr", value = "维保到期日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "deviceType", value = "打印类型", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "model", value = "资产型号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "serialNumber", value = "SN编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "isPasted", value = "是否打印", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "measureUnit", value = "计量单位", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "useYears", value = "使用年限", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "storageUser", value = "入库人", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "storageDateStart", value = "入库日期-开始时间", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "storageDateEnd", value = "入库日期-结束时间", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "brandId", value = "资产品牌", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "locationId", value = "存放地点", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "userId", value = "使用人", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "invoicePriceStr", value = "发票价格", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "invoiceDateStr", value = "发票日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "assetSource", value = "资产来源", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "updateDateStr", value = "更新日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "financeCode", value = "财务资产编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "supplierId", value = "供应商", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "isInventory", value = "是否盘点（0：否，1：是）", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "cancelAuth", value = "是否取消权限（0：否，1：是）", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "statusStr", value = "多个状态逗号分隔", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "bindAssets", value = "只在绑定资产分页传1", paramType = "query", dataType = "Integer")
	})
	@ApiOperationSupport(order = 19)
	@ApiOperation(value = "低值易耗分页", notes = "传入asset")
	public R<IPage<AssetDTO>> lowValueConsumablePage(@ApiIgnore AssetVO asset, Query query, SzykUser user) {
		asset.setRoleId(user == null ? null : Func.toLong(user.getRoleId()));
		IPage<AssetDTO> pages = assetService.page(Condition.getPage(query), asset);
		return R.data(pages);
	}

	/**
	 * 根据资产Key查询详情 资产档案表
	 */
	@GetMapping("/detailByKey")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "code", value = "资产编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "serialNumber", value = "资产SN编号", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 20)
	@ApiOperation(value = "根据资产Key查询详情", notes = "传入asset")
	public R<AssetDTO> detailByKey(@ApiIgnore AssetVO asset) {
		return R.data(assetService.detailByKey(asset));
	}

	/**
	 * 根据资产SN编号模糊查询详情 资产档案表
	 */
	@GetMapping("/detailBySn")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "code", value = "资产编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "serialNumber", value = "资产SN编号", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 21)
	@ApiOperation(value = "根据资产SN编号模糊查询详情", notes = "传入asset")
	public R<List<AssetDTO>> detailBySn(@ApiIgnore AssetVO asset) {
		return R.data(assetService.detailBySn(asset));
	}

	/**
	 * 修改操作记录分页 资产档案表
	 */
	@GetMapping("/updateLogPage")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "assetId", value = "资产id", paramType = "query", dataType = "long")
	})
	@ApiOperationSupport(order = 22)
	@ApiOperation(value = "修改操作记录分页", notes = "传入asset")
	public R<IPage<UpdateLogVO>> updateLogPage(@ApiIgnore UpdateLogVO updateLog, Query query) {
		return R.data(updateLogService.page(Condition.getPage(query), updateLog.getAssetId()));
	}

	@PostMapping("/updateAssetNewCode")
	@ApiOperationSupport(order = 23)
	@ApiOperation(value = "更新资产档案新财务资产编码", notes = "")
	public R updateAssetSupplier() {
		String updateSqlStr1 = "";
		List<Asset> list = assetService.list();
		if(Func.isNotEmpty(list)){
			for(Asset asset : list){
				if(Func.isNotEmpty(asset.getFinanceCode())){
					List<FinanceWbs> financeWbsList= financeWbsService.list(Wrappers.<FinanceWbs>query().lambda()
						.eq(FinanceWbs::getAssetNo, asset.getFinanceCode()));
					if(Func.isNotEmpty(financeWbsList)){
						String template = "update asset_asset set new_code=%s where id=%s;%n";
						updateSqlStr1 += String.format(template, financeWbsList.get(0).getAssetCode(), asset.getId());
					}
				}
				if(Func.isNotEmpty(asset.getWbsCode())){
					List<FinanceWbs> financeWbsList= financeWbsService.list(Wrappers.<FinanceWbs>query().lambda()
						.eq(FinanceWbs::getWbsCode, asset.getWbsCode()));
					if(Func.isNotEmpty(financeWbsList)){
						String template = "update asset_asset set new_code=%s where id=%s;%n";
						updateSqlStr1 += String.format(template, financeWbsList.get(0).getAssetCode(), asset.getId());
					}
				}
			}
		}
		log.info("更新资产档案新财务资产编码：{}", updateSqlStr1);
		return R.status(Boolean.TRUE);
	}


}
