package com.snszyk.asset.schedule;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.asset.entity.Asset;
import com.snszyk.asset.entity.Category;
import com.snszyk.asset.entity.Inventory;
import com.snszyk.asset.enums.AssetStatusEnum;
import com.snszyk.asset.enums.InventoryBizStatusEnum;
import com.snszyk.asset.enums.PrintTypeEnum;
import com.snszyk.asset.kafka.AssetKafkaService;
import com.snszyk.asset.service.IAssetService;
import com.snszyk.asset.service.ICategoryService;
import com.snszyk.asset.service.IInventoryService;
import com.snszyk.asset.wrapper.AssetWrapper;
import com.snszyk.asset.wrapper.CategoryWrapper;
import com.snszyk.asset.wrapper.InventoryWrapper;
import com.snszyk.common.constant.AssetConstant;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.message.enums.MessageBizTypeEnum;
import com.snszyk.message.enums.MessageTypeEnum;
import com.snszyk.message.enums.ReceiverTypeEnum;
import com.snszyk.message.feign.IMessageClient;
import com.snszyk.message.vo.MessageVo;
import com.snszyk.message.vo.ReceiverInfoVo;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Role;
import com.snszyk.system.user.entity.UserDept;
import com.snszyk.system.user.feign.IUserClient;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 定时任务
 *
 * <AUTHOR>
 * @date 2024/08/01 13:56
 **/
@Slf4j
@AllArgsConstructor
@Configuration
@EnableScheduling
public class AssetSchedule {

	private final IAssetService assetService;
	private final ICategoryService categoryService;
	private final IInventoryService inventoryService;
	private final AssetKafkaService assetKafkaService;
	private final IUserClient userClient;
	private final IMessageClient messageClient;
	private final SzykRedis szykRedis;
	private static final Integer SIZE = 10;
	private static final String ASSET_TO_LAKE_KEY = "szyk:asset::szyk:lake";
	private static final String CATEGORY_TO_LAKE_KEY = "szyk:category::szyk:lake";


	/**
	 * 盘点任务（每天8点进行）
	 *
	 * @return void
	 * <AUTHOR>
	 * @date 2024/08/01 13:56
	 */
    @Scheduled(cron = "0 0 8 * * ?")
	//@Scheduled(cron = "0 0/1 * * * ?")
    public void inventoryTask() {
		log.info("################盘点任务-START-################");
		List<Inventory> list = inventoryService.list(Wrappers.<Inventory>query().lambda()
			.eq(Inventory::getStatus, InventoryBizStatusEnum.DRAFT.getCode()));
		if(Func.isNotEmpty(list)){
			list.forEach(inventory -> {
				String startDate = DateUtil.format(inventory.getDate(), DateUtil.PATTERN_DATE);
				String now = DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE);
				if(now.equals(startDate)){
					if(Func.isNotEmpty(inventory.getFileId())){
						inventoryService.importTask(InventoryWrapper.build().entityVO(inventory));
					} else {
						inventoryService.task(InventoryWrapper.build().entityVO(inventory));
					}
					inventory.setStatus(InventoryBizStatusEnum.IN_PROGRESS.getCode());
					inventoryService.updateById(inventory);
				}
            });

		}
		log.info("################盘点任务-END-################");
    }

	/**
	 * 资产维保日期超时提醒（每日凌晨1点进行）
	 *
	 * @return void
	 * <AUTHOR>
	 * @date 2024/08/01 15:58
	 */
	@Scheduled(cron = "0 0 1 * * ?")
	//@Scheduled(cron = "0 0/1 * * * ?")
	public void maintainDateTimeout() {
		log.info("===================资产维保日期超时-START-===================");
		List<Integer> statusList = new ArrayList<>();
		statusList.add(AssetStatusEnum.IN_STOCK.getCode());
		statusList.add(AssetStatusEnum.IN_USE.getCode());
		statusList.add(AssetStatusEnum.STOPPED.getCode());
		Date now = DateUtil.parse(DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE), DateUtil.PATTERN_DATE);
		List<Asset> list = assetService.list(Wrappers.<Asset>query().lambda()
			.le(Asset::getMaintenanceExpireDate, now).in(Asset::getStatus, statusList));
		if(Func.isNotEmpty(list)){
			// 给综合服务中心角色发送消息
			String content = "";
			if(list.size() > SIZE){
				content = list.stream().limit(SIZE).map(asset ->
					asset.getName()+"("+asset.getCode()+")").collect(Collectors.joining(",")) + "......";
			} else {
				content = list.stream().map(asset ->
					asset.getName()+"("+asset.getCode()+")").collect(Collectors.joining(","));
			}
			MessageVo message = new MessageVo(list.get(0).getCode(), MessageBizTypeEnum.EXPIRE.name(),
				MessageBizTypeEnum.EXPIRE.getTitle(),MessageBizTypeEnum.EXPIRE.content(content, DateUtil.format(DateUtil.now(),
				DateUtil.PATTERN_DATE)), MessageTypeEnum.PUSH.getCode());
			Role role = SysCache.getRole(list.get(0).getTenantId(), AssetConstant.Asset.ISC_ADMIN);
			ReceiverInfoVo receiverInfoVo = new ReceiverInfoVo();
			ReceiverInfoVo.RoleVo roleVo = new ReceiverInfoVo.RoleVo();
			roleVo.setRoleId(role.getId()).setRoleName(role.getRoleName());
			receiverInfoVo.setRoleList(Arrays.asList(roleVo));
			message.setReceiverInfoVo(receiverInfoVo).setSendDingDing(Boolean.TRUE)
				.setReceiverType(ReceiverTypeEnum.ROLE.getCode());
			messageClient.pushMessage(message);
		}
		log.info("===================资产维保日期超时-END-===================");
	}

	/**
	 * 资产档案（办公设备）组织与用户不匹配提示（每15分钟执行一次）
	 *
	 * @return void
	 * <AUTHOR>
	 * @date 2024/11/12 16:56
	 */
	@Scheduled(cron = "0 0/15 * * * ?")
	public void deptUserNotMatch() {
		log.info("################监听资产档案组织与用户是否匹配-START-################");
		List<Asset> assetList = assetService.list(Wrappers.<Asset>query().lambda()
			.eq(Asset::getDeviceType, PrintTypeEnum.OFFICE.getCode())
			.isNotNull(Asset::getUseDept).isNotNull(Asset::getUserId)
			.select(Asset::getId, Asset::getUseDept, Asset::getUserId));
		if(Func.isNotEmpty(assetList)){
			assetList.forEach(asset -> {
				R<List<UserDept>> userDeptResult = userClient.deptListOfUser(asset.getUserId());
				if(userDeptResult.isSuccess() && Func.isNotEmpty(userDeptResult.getData())){
					List<UserDept> list = userDeptResult.getData();
					List<Long> deptIds = list.stream().map(UserDept::getDeptId).collect(Collectors.toList());
					if(!deptIds.contains(asset.getUseDept())){
						assetService.update(Wrappers.<Asset>update().lambda()
							.set(Asset::getIsMatched, 0).eq(Asset::getId, asset.getId()));
					} else {
						assetService.update(Wrappers.<Asset>update().lambda()
							.set(Asset::getIsMatched, 1).eq(Asset::getId, asset.getId()));
					}
				}
			});
		}
		log.info("################监听资产档案组织与用户是否匹配-END-################");
	}

	/**
	 * 已有资产数据分类初始化入湖（只执行一次）
	 *
	 * @return void
	 * <AUTHOR>
	 * @date 2025/04/23 09:56
	 */
	@Scheduled(cron = "0 0/10 * * * ?")
	public void initAssetCategoryToLake() {
		log.info("################资产分类初始化入湖任务-START-################");
		Object o = szykRedis.get(CATEGORY_TO_LAKE_KEY);
		if(Func.isNotEmpty(o)){
			log.info("################资产分类已经初始化-FINISH-################");
			return;
		}
		List<Category> list = categoryService.list(Wrappers.<Category>query().lambda()
			.eq(Category::getIsDeleted, 0).ne(Category::getCode, StringPool.ZERO + StringPool.ZERO));
		if(Func.isNotEmpty(list)){
			Date now = DateUtil.now();
			list.forEach(category -> {
				assetKafkaService.send(AssetConstant.Kafka.ASSET_CATEGORY_TOPIC,
					JSONUtil.toJsonStr(CategoryWrapper.build().entityCategoryLakeVO(category)));
			});
			szykRedis.set(CATEGORY_TO_LAKE_KEY, now.getTime());
		}
		log.info("################资产分类初始化入湖任务-END-################");
	}

	/**
	 * 已有资产数据（在用的关键基础设施）初始化入湖（只执行一次）
	 *
	 * @return void
	 * <AUTHOR>
	 * @date 2025/04/23 09:56
	 */
	@Scheduled(cron = "0 0/10 * * * ?")
	public void initAssetsToLake() {
		log.info("################资产数据初始化入湖任务-START-################");
		Object o = szykRedis.get(ASSET_TO_LAKE_KEY);
		if(Func.isNotEmpty(o)){
			log.info("################资产数据已经初始化-FINISH-################");
			return;
		}
		// 在用的是关键基础设施的资产
		List<Asset> list = assetService.list(Wrappers.<Asset>query().lambda()
			.eq(Asset::getPattern, 1).eq(Asset::getStatus, 1).eq(Asset::getIsKeyResources, 1));
		if(Func.isNotEmpty(list)){
			Date now = DateUtil.now();
			list.forEach(asset -> {
				assetKafkaService.send(AssetConstant.Kafka.ASSET_TOPIC,
					JSONUtil.toJsonStr(AssetWrapper.build().entityAssetLakeVO(asset)));
			});
			szykRedis.set(ASSET_TO_LAKE_KEY, now.getTime());
		}
		log.info("################资产数据初始化入湖任务-END-################");
	}


}
