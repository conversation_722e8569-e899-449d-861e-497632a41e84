/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.asset.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.asset.entity.Category;
import com.snszyk.asset.excel.CategoryExcel;
import com.snszyk.asset.vo.CategoryMcVO;
import com.snszyk.asset.vo.CategoryVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 资产分类表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
public interface CategoryMapper extends BaseMapper<Category> {

	/**
	 * 搜索分页
	 *
	 * @param page
	 * @param category
	 * @return
	 */
	List<Category> page(IPage page, @Param("category") CategoryVO category);

	/**
	 * 懒加载资产分类列表
	 *
	 * @param roleId
	 * @param parentCode
	 * @param param
	 * @return
	 */
	List<CategoryVO> lazyList(Long roleId, String parentCode, Map<String, Object> param);

	/**
	 * 懒加载资产分类列表
	 *
	 * @param parentCode
	 * @param roleId
	 * @param param
	 * @return
	 */
	List<CategoryVO> selectLazyList(String parentCode, Long roleId, Map<String, Object> param);

	/**
	 * 懒加载获取树形节点
	 *
	 * @param parentCode
	 * @param param
	 * @return
	 */
	List<CategoryVO> lazyTree(String parentCode, Map<String, Object> param);

	/**
	 * 根据父级获取子级列表
	 *
	 * @param parentCode
	 * @return
	 */
	List<CategoryVO> lazyTreeByParent(@Param("parentCode") String parentCode);

	/**
	 * 导出资产分类数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<CategoryExcel> exportCategory(@Param("ew") Wrapper<Category> queryWrapper);


}
