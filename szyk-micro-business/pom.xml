<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.snszyk</groupId>
        <artifactId>szyk-micro</artifactId>
        <version>1.3.0.RELEASE</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>szyk-micro-business</artifactId>
    <name>${project.artifactId}</name>
    <version>1.0.0.RELEASE</version>
    <packaging>pom</packaging>
    <description>Szyk 山能资产集合</description>

    <modules>
        <module>szyk-micro-asset</module>
        <module>szyk-micro-access</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-micro-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-starter-metrics</artifactId>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-starter-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-starter-api-crypto</artifactId>
        </dependency>
        <!--Oss-->
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-starter-oss</artifactId>
        </dependency>
        <!--MinIO-->
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-micro-dict-api</artifactId>
            <version>${szyk.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-micro-scope-api</artifactId>
            <version>${szyk.project.version}</version>
        </dependency>
    </dependencies>

</project>
